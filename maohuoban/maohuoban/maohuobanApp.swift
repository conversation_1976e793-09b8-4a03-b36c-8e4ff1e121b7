//
//  maohuobanApp.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//

import SwiftUI
import UIKit

@main
struct maohuobanApp: App {
    // 使用 UIHostingController 作为根视图控制器
    @UIApplicationDelegateAdaptor(AppDelegate.self) private var appDelegate
    @State private var isFirstLaunch = true

    var body: some Scene {
        WindowGroup {
            ContentView()
                .onAppear {
                    // 这里可以进行一些初始化操作
                    if isFirstLaunch {
                        // 首次启动时的操作
                        print("应用首次启动")
                        isFirstLaunch = false

                        // 可以在这里添加数据预加载、配置初始化等操作
                    }
                }
        }
    }
}

// AppDelegate 类，用于处理应用生命周期事件
class AppDelegate: NSObject, UIApplicationDelegate {
    var window: UIWindow?
    var appCoordinator: AppCoordinator?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil) -> Bool {
        // 在这里进行应用的初始化设置
        return true
    }

    // 如果需要使用 UIKit 的协调器系统，可以在这里设置
    func setupCoordinator(with window: UIWindow) {
        self.window = window
        let appCoordinator = AppCoordinator(window: window)
        self.appCoordinator = appCoordinator
        appCoordinator.start()
    }
}
