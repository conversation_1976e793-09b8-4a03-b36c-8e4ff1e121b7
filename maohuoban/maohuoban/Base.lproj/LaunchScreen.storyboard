<?xml version="1.0" encoding="UTF-8"?>
<!-- 毛伙伴启动图storyboard -->
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_3" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="402" height="874"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="毛伙伴" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Cw3-Ry-Qgf">
                                <rect key="frame" x="50" y="384.66666666666669" width="302" height="61"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="50"/>
                                <color key="textColor" red="0.0" green="0.47999999999999998" blue="0.80000000000000004" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <color key="shadowColor" white="0.66666666666666663" alpha="0.20000000000000001" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <size key="shadowOffset" width="1" height="1"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="连接爱宠，分享快乐" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dfc-Ry-Hgf">
                                <rect key="frame" x="50" y="465.66666666666669" width="302" height="24"/>
                                <fontDescription key="fontDescription" type="system" weight="medium" pointSize="20"/>
                                <color key="textColor" red="0.40000000000000002" green="0.40000000000000002" blue="0.40000000000000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="让每一个毛孩子都有自己的社交圈" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Efc-Ry-Hgf">
                                <rect key="frame" x="50" y="499.66666666666669" width="302" height="18.000000000000057"/>
                                <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                <color key="textColor" red="0.5" green="0.5" blue="0.5" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Cw3-Ry-Qgf" firstAttribute="centerX" secondItem="Ze5-6b-2t3" secondAttribute="centerX" id="Fw5-Ry-Qgf"/>
                            <constraint firstItem="Cw3-Ry-Qgf" firstAttribute="centerY" secondItem="Ze5-6b-2t3" secondAttribute="centerY" constant="-22" id="Gw6-Ry-Qgf"/>
                            <constraint firstItem="Cw3-Ry-Qgf" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="50" id="Iw8-Ry-Qgf"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="Cw3-Ry-Qgf" secondAttribute="trailing" constant="50" id="Jw9-Ry-Qgf"/>
                            <constraint firstItem="Dfc-Ry-Hgf" firstAttribute="top" secondItem="Cw3-Ry-Qgf" secondAttribute="bottom" constant="20" id="Kw0-Ry-Qgf"/>
                            <constraint firstItem="Dfc-Ry-Hgf" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="50" id="Lw1-Ry-Qgf"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="Dfc-Ry-Hgf" secondAttribute="trailing" constant="50" id="Mw2-Ry-Qgf"/>
                            <constraint firstItem="Efc-Ry-Hgf" firstAttribute="top" secondItem="Dfc-Ry-Hgf" secondAttribute="bottom" constant="10" id="Nw3-Ry-Qgf"/>
                            <constraint firstItem="Efc-Ry-Hgf" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="50" id="Ow4-Ry-Qgf"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="Efc-Ry-Hgf" secondAttribute="trailing" constant="50" id="Pw5-Ry-Qgf"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="53" y="375"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
