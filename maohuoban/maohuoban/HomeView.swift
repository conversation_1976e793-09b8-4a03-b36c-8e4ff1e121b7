//
//  HomeView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//

import SwiftUI

// MARK: - 主题常量
struct MHTheme {
    // 主题色 - 疗愈蓝 (在深色模式下稍微调亮)
    static let primaryColor = Color(red: 0.0, green: 0.48, blue: 0.8)
    static let secondaryColor = Color.red

    // 自适应颜色
    static let backgroundColor = Color(UIColor.systemBackground)
    static let secondaryBackgroundColor = Color(UIColor.secondarySystemBackground)
    static let tertiaryBackgroundColor = Color(UIColor.tertiarySystemBackground)

    // 文本颜色
    static let primaryTextColor = Color(UIColor.label)
    static let secondaryTextColor = Color(UIColor.secondaryLabel)
    static let tertiaryTextColor = Color(UIColor.tertiaryLabel)

    // 分割线和边框颜色
    static let separatorColor = Color(UIColor.separator)
    static let borderColor = Color(UIColor.separator).opacity(0.5)

    // 灰色背景
    static let lightGrayColor = Color(UIColor.systemGray6)
    static let mediumGrayColor = Color(UIColor.systemGray5)
    static let darkGrayColor = Color(UIColor.systemGray4)
}

// MARK: - 主视图
struct HomeView: View {
    // 状态变量
    @State private var selectedTab = 0
    @State private var bottomSafeArea: CGFloat = 0

    // 标签栏基础高度
    private let tabBarBaseHeight: CGFloat = 50

    var body: some View {
        ZStack(alignment: .bottom) {
            // 主内容区域
            TabView(selection: $selectedTab) {
                // 首页
                FeedView()
                    .tag(0)

                // 热门
                HotView()
                    .tag(1)

                // 占位视图（发布按钮）
                Color.clear
                    .tag(2)

                // 消息
                MessageView()
                    .tag(3)

                // 我的
                ProfileView()
                    .tag(4)
            }
            .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            .ignoresSafeArea(.keyboard) // 忽略键盘
            .safeAreaInset(edge: .bottom) {
                // 预留TabBar空间，使内容不被TabBar遮挡
                Color.clear
                    .frame(height: tabBarBaseHeight)
            }

            // 自定义底部导航栏
            VStack(spacing: 0) {
                // Tab栏
                MHTabBar(selectedTab: $selectedTab)

                // 底部安全区域
                Spacer()
                    .frame(maxWidth: .infinity)
                    .frame(height: bottomSafeArea)
                    .background(Color(UIColor.systemBackground))
            }
            .background(Color.clear)
        }
        .ignoresSafeArea(.all, edges: .bottom)
        // 使用背景GeometryReader获取安全区域高度
        .background(
            GeometryReader { proxy in
                Color.clear.onAppear {
                    updateSafeAreaInsets()
                }
            }
        )
        .onAppear {
            updateSafeAreaInsets()
        }
    }

    // 更新底部安全区域高度
    private func updateSafeAreaInsets() {
        DispatchQueue.main.async {
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first else {
                bottomSafeArea = 0
                return
            }
            bottomSafeArea = window.safeAreaInsets.bottom
        }
    }
}

// MARK: - 自定义底部导航栏
struct MHTabBar: View {
    @Binding var selectedTab: Int

    // 底部导航项 - 只显示文字
    private let tabItems = ["首页", "社区", "", "消息", "我"]

    var body: some View {
        HStack(spacing: 0) {
            ForEach(0..<5) { index in
                if index == 2 {
                    // 中间的发布按钮 - 椭圆形带白色加号，圆角更小
                    Button(action: {
                        // 显示发布界面
                    }) {
                        ZStack {
                            // 椭圆形背景，使用疗愈蓝，圆角更小
                            RoundedRectangle(cornerRadius: 10) // 减小圆角
                                .foregroundColor(MHTheme.primaryColor)
                                .frame(width: 65, height: 42) // 减少宽度，增加高度

                            // 白色加号
                            Image(systemName: "plus")
                                .font(.system(size: 18, weight: .bold))
                                .foregroundColor(.white)
                        }
                    }
                    .offset(y: 1) // 减小偏移，使按钮更靠近底部
                    .frame(width: UIScreen.main.bounds.width / 5)
                } else {
                    // 其他导航按钮 - 只显示文字，不显示图标
                    Button(action: {
                        selectedTab = index
                    }) {
                        Text(tabItems[index])
                            .font(.system(size: 17, weight: selectedTab == index ? .bold : .regular)) // 与主标签一致，选中时加粗
                            .foregroundColor(selectedTab == index ? MHTheme.primaryTextColor : MHTheme.secondaryTextColor) // 使用自适应文本颜色
                            .frame(width: UIScreen.main.bounds.width / 5, height: 50)
                    }
                }
            }
        }
        .background(MHTheme.backgroundColor)
        .cornerRadius(15, corners: [.topLeft, .topRight])
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: -2)
    }
}

// MARK: - 首页 Feed 视图
struct FeedView: View {
    // 状态变量
    @State private var selectedChannel = 1 // 默认选中"发现"(索引1)
    @State private var searchText = ""

    // 主标签选项 - 包含附近、关注、发现
    private let subChannels = [ "关注", "发现","附近" ]

    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航区域
            MHTopNavigationBar(
                searchText: $searchText,
                selectedMainTab: $selectedChannel,
                mainTabs: subChannels,
                showLocationIcon: false // 不再单独显示位置
            )

            // 内容区域 - 直接显示瀑布流，移除频道选择器
            ScrollView {
                // 瀑布流内容
                MHWaterfallLayout()
            }
        }
        .navigationBarHidden(true)
    }
}

// MARK: - 顶部导航栏组件
struct MHTopNavigationBar: View {
    @Binding var searchText: String
    @Binding var selectedMainTab: Int
    let mainTabs: [String]
    let showLocationIcon: Bool

    // 红点数字 - 注释掉，后续根据API实现
    // private let notificationCount = 18

    var body: some View {
        ZStack {
            HStack {
                // 菜单按钮 - 自定义三条线，增加垂直间距
                Button(action: {
                    // 打开菜单
                }) {
                    VStack(spacing: 5) { // 适当的垂直间距
                        ForEach(0..<3) { _ in
                            Rectangle()
                                .fill(MHTheme.primaryTextColor) // 使用自适应文本颜色
                                .frame(width: 22, height: 2)
                                .cornerRadius(1)
                        }
                    }
                    .frame(width: 22, height: 24) // 增加整体高度，确保有足够空间
                    .padding(4) // 增加点击区域
                }

                Spacer()

                // 搜索按钮
                Button(action: {
                    // 打开搜索
                }) {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 20))
                        .foregroundColor(MHTheme.primaryTextColor) // 使用自适应文本颜色
                }
            }

            // 主标签切换 - 居中显示
            HStack(spacing: 25) {
                ForEach(0..<mainTabs.count, id: \.self) { index in
                    Button(action: {
                        selectedMainTab = index
                    }) {
                        Text(mainTabs[index])
                            .font(.system(size: 16, weight: selectedMainTab == index ? .bold : .regular))
                            .foregroundColor(selectedMainTab == index ? MHTheme.primaryTextColor : MHTheme.secondaryTextColor)
                            .padding(.bottom, 5)
                            .overlay(
                                Rectangle()
                                    .frame(height: 2)
                                    .foregroundColor(selectedMainTab == index ? MHTheme.primaryColor : .clear) // 使用疗愈蓝而不是红色
                                    .offset(y: 4),
                                alignment: .bottom
                            )
                    }
                }
            }
        }
        .padding(.horizontal, 15)
        .padding(.top, 10)
        .padding(.bottom, 5)
    }
}

// MARK: - 频道选择器组件
struct MHChannelSelector: View {
    @Binding var selectedChannel: Int
    let channels: [String]

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 25) {
                ForEach(0..<channels.count, id: \.self) { index in
                    Button(action: {
                        selectedChannel = index
                    }) {
                        VStack(spacing: 5) {
                            Text(channels[index])
                                .font(.system(size: 15))
                                .foregroundColor(selectedChannel == index ? MHTheme.primaryTextColor : MHTheme.secondaryTextColor)

                            // 下划线指示器
                            if selectedChannel == index {
                                Rectangle()
                                    .fill(MHTheme.primaryColor)
                                    .frame(width: 20, height: 2)
                            } else {
                                Rectangle()
                                    .fill(Color.clear)
                                    .frame(width: 20, height: 2)
                            }
                        }
                    }
                }

                // 更多按钮
                Button(action: {
                    // 显示更多频道
                }) {
                    Image(systemName: "chevron.down")
                        .font(.system(size: 15))
                        .foregroundColor(MHTheme.secondaryTextColor)
                }
            }
            .padding(.horizontal, 15)
            .padding(.vertical, 5)
        }
        .background(MHTheme.backgroundColor)
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(Color.gray.opacity(0.3)),
            alignment: .bottom
        )
    }
}

// MARK: - 瀑布流布局组件
struct MHWaterfallLayout: View {
    // 示例数据
    private let leftItems = [
        FeedItem(id: 1, title: "奔跑吧已经13季就一季来过上海，之后怎么再也不来了吗？是因为费用太高了吗？还是就他们之后再也不来了~", author: "奶茶爱冰冰", likes: 34, hasImage: false),
        FeedItem(id: 3, title: "手动挡直接踩刹车停车会熄火吗？", author: "", likes: 0, hasImage: true)
    ]

    private let rightItems = [
        FeedItem(id: 2, title: "奇瑞这波操作真的666", author: "汽车信息", likes: 73, hasImage: true),
        FeedItem(id: 4, title: "自建了一个小型光伏电站，3块板日发电4度", author: "Zeruns", likes: 66, hasImage: true),
        FeedItem(id: 5, title: "Henry的Apple Watch", author: "", likes: 0, hasImage: true)
    ]

    var body: some View {
        HStack(alignment: .top, spacing: 10) {
            // 左列
            VStack(spacing: 10) {
                ForEach(leftItems) { item in
                    MHFeedCard(item: item)
                }
            }
            .frame(width: UIScreen.main.bounds.width * 0.48)

            // 右列
            VStack(spacing: 10) {
                ForEach(rightItems) { item in
                    MHFeedCard(item: item)
                }
            }
            .frame(width: UIScreen.main.bounds.width * 0.48)
        }
        .padding(.horizontal, 5)
        .padding(.top, 10)
    }
}

// MARK: - Feed 卡片组件
struct MHFeedCard: View {
    let item: FeedItem

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 内容文字
            if !item.hasImage || (item.hasImage && item.title.count > 0) {
                Text(item.title)
                    .font(.system(size: 15))
                    .lineLimit(item.hasImage ? 2 : 6)
                    .padding(.horizontal, 10)
                    .padding(.top, 10)
            }

            // 内容图片
            if item.hasImage {
                ZStack {
                    Rectangle()
                        .fill(MHTheme.lightGrayColor)
                        .aspectRatio(1.0, contentMode: .fit)

                    // 如果是视频，显示播放按钮
                    if item.id == 5 {
                        Image(systemName: "play.circle")
                            .font(.system(size: 30))
                            .foregroundColor(.white)
                    }
                }
            }

            // 用户信息和互动
            if item.author.count > 0 {
                HStack {
                    // 用户头像和名称
                    HStack(spacing: 5) {
                        Circle()
                            .fill(MHTheme.mediumGrayColor)
                            .frame(width: 20, height: 20)

                        Text(item.author)
                            .font(.system(size: 12))
                            .foregroundColor(MHTheme.secondaryTextColor)
                    }

                    Spacer()

                    // 点赞数
                    HStack(spacing: 3) {
                        Image(systemName: "heart")
                            .font(.system(size: 12))

                        Text("\(item.likes)")
                            .font(.system(size: 12))
                    }
                    .foregroundColor(MHTheme.secondaryTextColor)
                }
                .padding(.horizontal, 10)
                .padding(.bottom, 10)
            }
        }
        .background(MHTheme.backgroundColor)
        .cornerRadius(8)
    }
}

// MARK: - 热门视图
struct HotView: View {
    var body: some View {
        NavigationView {
            Text("热门页面")
                .navigationTitle("热门")
        }
    }
}

// MARK: - 消息视图
struct MessageView: View {
    var body: some View {
        NavigationView {
            Text("消息页面")
                .navigationTitle("消息")
        }
    }
}

// MARK: - 个人资料视图
struct ProfileView: View {
    var body: some View {
        NavigationView {
            Text("个人资料页面")
                .navigationTitle("我的")
        }
    }
}

// MARK: - 数据模型
struct FeedItem: Identifiable {
    let id: Int
    let title: String
    let author: String
    let likes: Int
    let hasImage: Bool
}

// MARK: - 扩展
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// MARK: - 预览
#Preview {
    HomeView()
}

