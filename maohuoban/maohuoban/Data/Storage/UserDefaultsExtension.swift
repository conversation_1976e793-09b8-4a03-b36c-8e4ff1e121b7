import Foundation

/// UserDefaults扩展，简化常用操作
extension UserDefaults {
    /// 枚举定义所有UserDefaults键
    enum Key: String {
        case isLoggedIn
        case userId
        case username
        case userEmail
        case userAvatar
        case appTheme
        case lastRefreshTime
        // 添加更多键...
    }
    
    /// 便捷方法：设置值
    func set(_ value: Any?, forKey key: Key) {
        set(value, forKey: key.rawValue)
    }
    
    /// 便捷方法：获取字符串
    func string(forKey key: Key) -> String? {
        return string(forKey: key.rawValue)
    }
    
    /// 便捷方法：获取整数
    func integer(forKey key: Key) -> Int {
        return integer(forKey: key.rawValue)
    }
    
    /// 便捷方法：获取布尔值
    func bool(forKey key: Key) -> Bool {
        return bool(forKey: key.rawValue)
    }
    
    /// 便捷方法：获取字典
    func dictionary(forKey key: Key) -> [String: Any]? {
        return dictionary(forKey: key.rawValue)
    }
    
    /// 便捷方法：获取数组
    func array(forKey key: Key) -> [Any]? {
        return array(forKey: key.rawValue)
    }
    
    /// 便捷方法：获取日期
    func date(forKey key: Key) -> Date? {
        return object(forKey: key.rawValue) as? Date
    }
    
    /// 便捷方法：移除对象
    func removeObject(forKey key: Key) {
        removeObject(forKey: key.rawValue)
    }
    
    /// 便捷方法：编码对象
    func set<T: Encodable>(_ encodable: T, forKey key: Key) {
        if let data = try? JSONEncoder().encode(encodable) {
            set(data, forKey: key.rawValue)
        }
    }
    
    /// 便捷方法：解码对象
    func object<T: Decodable>(_ type: T.Type, forKey key: Key) -> T? {
        guard let data = data(forKey: key.rawValue) else {
            return nil
        }
        
        return try? JSONDecoder().decode(type, from: data)
    }
}
