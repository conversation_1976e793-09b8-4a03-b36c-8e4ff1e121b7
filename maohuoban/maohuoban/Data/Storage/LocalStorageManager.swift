import Foundation

/// 本地存储协议
protocol LocalStorageProtocol {
    /// 保存对象
    /// - Parameters:
    ///   - object: 要保存的对象
    ///   - key: 键
    func save<T: Encodable>(_ object: T, forKey key: String) throws
    
    /// 加载对象
    /// - Parameter key: 键
    /// - Returns: 解码后的对象，如果不存在则返回nil
    func load<T: Decodable>(forKey key: String) -> T?
    
    /// 删除对象
    /// - Parameter key: 键
    func delete(forKey key: String)
    
    /// 清除所有数据
    func clearAll()
}

/// 本地存储管理器，用于处理更复杂的数据存储需求
class LocalStorageManager: LocalStorageProtocol {
    /// 单例实例
    static let shared = LocalStorageManager()
    
    private let userDefaults = UserDefaults.standard
    
    private init() {}
    
    /// 保存对象
    /// - Parameters:
    ///   - object: 要保存的对象
    ///   - key: 键
    func save<T: Encodable>(_ object: T, forKey key: String) throws {
        let data = try JSONEncoder().encode(object)
        userDefaults.set(data, forKey: key)
    }
    
    /// 加载对象
    /// - Parameter key: 键
    /// - Returns: 解码后的对象，如果不存在则返回nil
    func load<T: Decodable>(forKey key: String) -> T? {
        guard let data = userDefaults.data(forKey: key) else {
            return nil
        }
        
        return try? JSONDecoder().decode(T.self, from: data)
    }
    
    /// 删除对象
    /// - Parameter key: 键
    func delete(forKey key: String) {
        userDefaults.removeObject(forKey: key)
    }
    
    /// 清除所有数据
    func clearAll() {
        let domain = Bundle.main.bundleIdentifier!
        userDefaults.removePersistentDomain(forName: domain)
        userDefaults.synchronize()
    }
}
