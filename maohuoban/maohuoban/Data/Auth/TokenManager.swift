import Foundation

/// Token管理器，负责管理认证令牌的存储和刷新
class TokenManager {
    /// 单例实例
    static let shared = TokenManager()
    
    private let userDefaults = UserDefaults.standard
    private let keychainService = "com.maohuoban.auth"
    
    private let accessTokenKey = "accessToken"
    private let refreshTokenKey = "refreshToken"
    private let tokenExpirationKey = "tokenExpiration"
    
    private init() {}
    
    /// 访问令牌
    var accessToken: String? {
        get {
            return userDefaults.string(forKey: accessTokenKey)
        }
        set {
            userDefaults.set(newValue, forKey: accessTokenKey)
        }
    }
    
    /// 刷新令牌 (应该存储在Keychain中，这里简化为UserDefaults)
    var refreshToken: String? {
        get {
            return userDefaults.string(forKey: refreshTokenKey)
        }
        set {
            userDefaults.set(newValue, forKey: refreshTokenKey)
        }
    }
    
    /// 令牌过期时间
    var tokenExpiration: Date? {
        get {
            return userDefaults.object(forKey: tokenExpirationKey) as? Date
        }
        set {
            userDefaults.set(newValue, forKey: tokenExpirationKey)
        }
    }
    
    /// 保存令牌
    /// - Parameters:
    ///   - accessToken: 访问令牌
    ///   - refreshToken: 刷新令牌
    ///   - expiresIn: 过期时间（秒）
    func saveTokens(accessToken: String, refreshToken: String, expiresIn: Int) {
        self.accessToken = accessToken
        self.refreshToken = refreshToken
        
        // 计算过期时间
        let expirationDate = Date().addingTimeInterval(TimeInterval(expiresIn))
        self.tokenExpiration = expirationDate
    }
    
    /// 清除令牌
    func clearTokens() {
        accessToken = nil
        refreshToken = nil
        tokenExpiration = nil
    }
    
    /// 检查令牌是否有效
    var isTokenValid: Bool {
        guard let expiration = tokenExpiration, let _ = accessToken else {
            return false
        }
        
        // 提前5分钟认为令牌过期，以便有时间刷新
        let gracePeriod: TimeInterval = 5 * 60
        return expiration.timeIntervalSinceNow > gracePeriod
    }
}
