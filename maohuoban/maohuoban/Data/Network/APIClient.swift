import Foundation
import Combine

/// API客户端实现，负责处理网络请求
class APIClient: APIClientProtocol {
    /// 单例实例
    static let shared = APIClient()
    
    private let baseURL: String
    private let session: URLSession
    private let decoder: JSONDecoder
    
    private init() {
        self.baseURL = "http://api.maohuoban.com/v1" // 基础URL，可以从配置文件读取
        self.session = URLSession.shared
        
        self.decoder = JSONDecoder()
        self.decoder.keyDecodingStrategy = .convertFromSnakeCase // 将snake_case转换为camelCase
        self.decoder.dateDecodingStrategy = .iso8601 // 使用ISO8601格式解析日期
    }
    
    /// 发送请求并返回Publisher
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - method: HTTP方法
    ///   - parameters: 请求参数
    ///   - headers: 自定义请求头
    /// - Returns: 包含解码后数据的Publisher
    func request<T: Decodable>(
        endpoint: APIEndpoint,
        method: HTTPMethod = .get,
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) -> AnyPublisher<T, APIError> {
        // 构建URL
        guard let url = URL(string: baseURL + endpoint.path) else {
            return Fail(error: APIError.invalidURL).eraseToAnyPublisher()
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        
        // 添加默认头部
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 添加自定义头部
        headers?.forEach { key, value in
            request.addValue(value, forHTTPHeaderField: key)
        }
        
        // 添加认证Token
        if let token = TokenManager.shared.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        // 添加请求参数
        if let parameters = parameters {
            if method == .get {
                // 对于GET请求，将参数添加到URL
                var components = URLComponents(url: url, resolvingAgainstBaseURL: false)!
                components.queryItems = parameters.map { key, value in
                    URLQueryItem(name: key, value: "\(value)")
                }
                request.url = components.url
            } else {
                // 对于其他请求，将参数添加到请求体
                do {
                    request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
                } catch {
                    return Fail(error: APIError.requestFailed(error)).eraseToAnyPublisher()
                }
            }
        }
        
        // 发送请求
        return session.dataTaskPublisher(for: request)
            .mapError { APIError.requestFailed($0) }
            .flatMap { data, response -> AnyPublisher<T, APIError> in
                guard let httpResponse = response as? HTTPURLResponse else {
                    return Fail(error: APIError.invalidResponse).eraseToAnyPublisher()
                }
                
                // 处理HTTP状态码
                switch httpResponse.statusCode {
                case 200...299:
                    // 成功
                    do {
                        // 尝试解析为APIResponse<T>
                        let apiResponse = try self.decoder.decode(APIResponse<T>.self, from: data)
                        return Just(apiResponse.data)
                            .setFailureType(to: APIError.self)
                            .eraseToAnyPublisher()
                    } catch {
                        // 如果解析APIResponse失败，尝试直接解析为T
                        do {
                            let value = try self.decoder.decode(T.self, from: data)
                            return Just(value)
                                .setFailureType(to: APIError.self)
                                .eraseToAnyPublisher()
                        } catch {
                            return Fail(error: APIError.decodingFailed(error)).eraseToAnyPublisher()
                        }
                    }
                case 401:
                    // 未授权，需要刷新Token
                    return Fail(error: APIError.unauthorized).eraseToAnyPublisher()
                case 400...499:
                    // 客户端错误
                    do {
                        let errorResponse = try self.decoder.decode(ErrorResponse.self, from: data)
                        return Fail(error: APIError.serverError(code: httpResponse.statusCode, message: errorResponse.message)).eraseToAnyPublisher()
                    } catch {
                        return Fail(error: APIError.serverError(code: httpResponse.statusCode, message: "客户端错误")).eraseToAnyPublisher()
                    }
                case 500...599:
                    // 服务器错误
                    do {
                        let errorResponse = try self.decoder.decode(ErrorResponse.self, from: data)
                        return Fail(error: APIError.serverError(code: httpResponse.statusCode, message: errorResponse.message)).eraseToAnyPublisher()
                    } catch {
                        return Fail(error: APIError.serverError(code: httpResponse.statusCode, message: "服务器错误")).eraseToAnyPublisher()
                    }
                default:
                    return Fail(error: APIError.unknown).eraseToAnyPublisher()
                }
            }
            .eraseToAnyPublisher()
    }
}
