import Foundation

/// API端点枚举，定义了所有API路径
enum APIEndpoint {
    // 用户相关
    case login
    case register
    case refreshToken
    case logout
    case getUserProfile(userId: String)
    case updateUserProfile(userId: String)
    case getUserFollowings(userId: String)
    case getUserFollowers(userId: String)
    case followUser(userId: String)
    case unfollowUser(userId: String)
    
    // 宠物相关
    case getPets
    case getPet(petId: String)
    case createPet
    case updatePet(petId: String)
    case deletePet(petId: String)
    
    // 动态相关
    case getPosts
    case getPost(postId: String)
    case createPost
    case updatePost(postId: String)
    case deletePost(postId: String)
    case likePost(postId: String)
    case unlikePost(postId: String)
    case getPostComments(postId: String)
    case createPostComment(postId: String)
    
    /// 获取API路径
    var path: String {
        switch self {
        // 用户相关
        case .login:
            return "/users/login"
        case .register:
            return "/users/register"
        case .refreshToken:
            return "/users/refresh-token"
        case .logout:
            return "/users/logout"
        case .getUserProfile(let userId):
            return "/users/\(userId)"
        case .updateUserProfile(let userId):
            return "/users/\(userId)"
        case .getUserFollowings(let userId):
            return "/users/\(userId)/followings"
        case .getUserFollowers(let userId):
            return "/users/\(userId)/followers"
        case .followUser(let userId):
            return "/users/\(userId)/follow"
        case .unfollowUser(let userId):
            return "/users/\(userId)/unfollow"
            
        // 宠物相关
        case .getPets:
            return "/pets"
        case .getPet(let petId):
            return "/pets/\(petId)"
        case .createPet:
            return "/pets"
        case .updatePet(let petId):
            return "/pets/\(petId)"
        case .deletePet(let petId):
            return "/pets/\(petId)"
            
        // 动态相关
        case .getPosts:
            return "/posts"
        case .getPost(let postId):
            return "/posts/\(postId)"
        case .createPost:
            return "/posts"
        case .updatePost(let postId):
            return "/posts/\(postId)"
        case .deletePost(let postId):
            return "/posts/\(postId)"
        case .likePost(let postId):
            return "/posts/\(postId)/like"
        case .unlikePost(let postId):
            return "/posts/\(postId)/unlike"
        case .getPostComments(let postId):
            return "/posts/\(postId)/comments"
        case .createPostComment(let postId):
            return "/posts/\(postId)/comments"
        }
    }
}
