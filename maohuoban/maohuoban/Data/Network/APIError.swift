import Foundation

/// API错误枚举，定义了网络请求可能遇到的各种错误类型
enum APIError: Error {
    case invalidURL                              // 无效的URL
    case requestFailed(Error)                    // 请求失败，包含原始错误
    case invalidResponse                         // 无效的响应
    case decodingFailed(Error)                   // 数据解析失败，包含原始错误
    case serverError(code: Int, message: String) // 服务器返回的错误，包含错误码和消息
    case unauthorized                            // 未授权，需要重新登录
    case unknown                                 // 未知错误
    
    /// 错误的本地化描述
    var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .requestFailed(let error):
            return "请求失败: \(error.localizedDescription)"
        case .invalidResponse:
            return "无效的响应"
        case .decodingFailed(let error):
            return "数据解析失败: \(error.localizedDescription)"
        case .serverError(let code, let message):
            return "服务器错误 \(code): \(message)"
        case .unauthorized:
            return "未授权，请重新登录"
        case .unknown:
            return "未知错误"
        }
    }
}
