import Foundation
import Combine

/// API客户端协议，定义了网络请求的基本方法
protocol APIClientProtocol {
    /// 发送请求并返回Publisher
    /// - Parameters:
    ///   - endpoint: API端点
    ///   - method: HTTP方法
    ///   - parameters: 请求参数
    ///   - headers: 自定义请求头
    /// - Returns: 包含解码后数据的Publisher
    func request<T: Decodable>(
        endpoint: APIEndpoint,
        method: HTTPMethod,
        parameters: [String: Any]?,
        headers: [String: String]?
    ) -> AnyPublisher<T, APIError>
}
