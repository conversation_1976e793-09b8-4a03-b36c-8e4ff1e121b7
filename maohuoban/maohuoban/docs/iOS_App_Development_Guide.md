# iOS应用开发指南

## 目录
1. [项目概述](#项目概述)
2. [开发环境准备](#开发环境准备)
3. [项目结构](#项目结构)
4. [开发步骤](#开发步骤)
5. [API接口文档](#API接口文档)
6. [认证与授权](#认证与授权)
7. [常见问题](#常见问题)

## 项目概述

本项目（毛伙伴）是一个宠物社交平台的iOS客户端应用，与已有的Go后端服务进行交互。应用主要功能包括：

- 用户认证与个人资料管理
- 宠物信息管理
- 社交动态发布与互动
- 宠物交易市场
- 社区活动与讨论
- 即时通讯
- 搜索功能
- 数据分析与统计
- 安全与隐私保护

## 开发环境准备

### 必要工具
- macOS操作系统
- Xcode 14.0或更高版本
- Swift 5.0或更高版本
- CocoaPods或Swift Package Manager (SPM)
- Git版本控制

### 推荐工具
- Postman（API测试）
- <PERSON>（网络调试代理）
- Figma或Sketch（UI设计）
- SwiftLint（代码规范检查）

### 环境配置步骤
1. 安装最新版Xcode
2. 安装CocoaPods: `sudo gem install cocoapods`
3. 克隆项目仓库: `git clone [项目仓库URL]`
4. 进入项目目录: `cd [项目目录]`
5. 安装依赖: `pod install`
6. 打开工作区文件: `open [项目名].xcworkspace`

## 项目结构

推荐采用MVVM+Coordinator架构模式，项目结构如下：

```
[maohuoabn_ios]/
├── Application/                # 应用程序层
│   ├── AppDelegate.swift
│   ├── SceneDelegate.swift
│   └── AppCoordinator.swift
├── Presentation/              # 表现层
│   ├── Common/                # 通用UI组件
│   ├── Screens/               # 各个功能模块的屏幕
│   │   ├── Auth/              # 认证模块
│   │   ├── Profile/           # 个人资料模块
│   │   ├── Pet/               # 宠物模块
│   │   ├── Post/              # 动态模块
│   │   ├── Trade/             # 交易模块
│   │   ├── Community/         # 社区模块
│   │   ├── Chat/              # 聊天模块
│   │   ├── Search/            # 搜索模块
│   │   └── Analytics/         # 数据分析模块
│   └── Coordinators/          # 协调器
├── Domain/                    # 领域层
│   ├── Models/                # 领域模型
│   ├── UseCases/              # 用例
│   └── Repositories/          # 仓库接口
├── Data/                      # 数据层
│   ├── Network/               # 网络相关
│   │   ├── APIClient.swift
│   │   ├── Endpoints.swift
│   │   └── DTOs/              # 数据传输对象
│   ├── Repositories/          # 仓库实现
│   └── PersistentStorage/     # 本地存储
├── Utils/                     # 工具类
│   ├── Extensions/            # Swift扩展
│   ├── Helpers/               # 辅助类
│   └── Constants.swift
├── Resources/                 # 资源文件
│   ├── Assets.xcassets
│   ├── Localizable.strings
│   └── Info.plist
└── Tests/                     # 测试
    ├── UnitTests/
    └── UITests/
```

## 开发步骤

### 阶段1：项目初始化与基础架构（2周）
1. 创建Xcode项目
2. 配置项目结构
3. 集成必要的第三方库
4. 实现网络层基础架构
5. 实现本地存储方案
6. 设计基础UI组件
7. 实现导航与路由系统

**完成标准**：
- 项目能够成功编译运行
- 网络层能够发送基本请求
- 本地存储能够正常工作
- 基础UI组件库已建立
- 导航系统能够正常工作

### 阶段2：用户认证与个人资料（2周）
1. 实现登录/注册界面
2. 集成JWT认证
3. 实现个人资料查看与编辑
4. 实现用户关注功能
5. 实现设置界面

**完成标准**：
- 用户能够注册新账号
- 用户能够登录并保持登录状态
- 用户能够查看和编辑个人资料
- 用户能够关注/取消关注其他用户
- 用户能够修改应用设置

### 阶段3：宠物管理与动态发布（3周）
1. 实现宠物信息管理
2. 实现动态发布功能
3. 实现动态列表与详情
4. 实现评论与点赞功能
5. 实现图片/视频上传

**完成标准**：
- 用户能够添加、编辑和删除宠物信息
- 用户能够发布包含文字、图片和视频的动态
- 用户能够查看动态列表和详情
- 用户能够对动态进行评论和点赞
- 媒体上传功能正常工作

### 阶段4：交易市场（3周）
1. 实现商品列表与详情
2. 实现商品发布功能
3. 实现商品搜索与筛选
4. 实现交易流程
5. 实现议价功能

**完成标准**：
- 用户能够浏览商品列表和详情
- 用户能够发布商品
- 用户能够搜索和筛选商品
- 用户能够进行交易操作
- 用户能够进行议价

### 阶段5：社区功能（2周）
1. 实现活动列表与详情
2. 实现小组列表与详情
3. 实现讨论功能
4. 实现活动报名功能

**完成标准**：
- 用户能够浏览活动列表和详情
- 用户能够浏览小组列表和详情
- 用户能够参与讨论
- 用户能够报名参加活动

### 阶段6：聊天功能（3周）
1. 实现WebSocket连接
2. 实现会话列表与详情
3. 实现消息发送与接收
4. 实现消息通知
5. 实现多媒体消息支持

**完成标准**：
- 用户能够查看会话列表
- 用户能够发送和接收文字消息
- 用户能够发送和接收图片、语音等多媒体消息
- 用户能够收到消息通知
- 聊天功能在前后台都能正常工作

### 阶段7：搜索与推荐（2周）
1. 实现全局搜索功能
2. 实现搜索结果展示
3. 实现内容推荐功能
4. 实现搜索历史与热门搜索

**完成标准**：
- 用户能够搜索用户、宠物、动态、商品等内容
- 搜索结果能够正确展示
- 用户能够查看推荐内容
- 用户能够查看搜索历史和热门搜索

### 阶段8：数据分析与安全（2周）
1. 实现用户行为统计
2. 实现内容分析功能
3. 实现安全策略
4. 实现隐私保护功能

**完成标准**：
- 应用能够收集用户行为数据
- 应用能够展示数据分析结果
- 应用实现了必要的安全措施
- 用户隐私得到保护

### 阶段9：优化与测试（3周）
1. 性能优化
2. UI/UX优化
3. 单元测试
4. UI测试
5. 用户测试

**完成标准**：
- 应用性能达到要求
- UI/UX符合设计规范
- 单元测试覆盖率达到80%以上
- UI测试覆盖主要功能
- 用户测试反馈问题已解决

## API接口文档

后端API基础URL: `http://your-server-address/api/v1`

### 认证相关API
- 登录: `POST /users/login`
  ```json
  // 请求体
  {
    "email": "<EMAIL>",
    "password": "password123"
  }

  // 响应体
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "user": {
        "id": "user123",
        "username": "username",
        "email": "<EMAIL>",
        "avatar": "http://example.com/avatar.jpg"
      },
      "token": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 3600
      }
    }
  }
  ```

- 注册: `POST /users/register`
  ```json
  // 请求体
  {
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirm_password": "password123"
  }

  // 响应体
  {
    "code": 200,
    "message": "注册成功",
    "data": {
      "user": {
        "id": "user456",
        "username": "newuser",
        "email": "<EMAIL>"
      }
    }
  }
  ```

- 刷新Token: `POST /users/refresh-token`
  ```json
  // 请求体
  {
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }

  // 响应体
  {
    "code": 200,
    "message": "刷新成功",
    "data": {
      "token": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 3600
      }
    }
  }
  ```

- 登出: `POST /users/logout`
  ```json
  // 请求头
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

  // 响应体
  {
    "code": 200,
    "message": "登出成功"
  }
  ```

### 用户相关API
- 获取用户信息: `GET /users/:id`
  ```json
  // 请求头
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

  // 响应体
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "user": {
        "id": "user123",
        "username": "username",
        "email": "<EMAIL>",
        "avatar": "http://example.com/avatar.jpg",
        "bio": "用户简介",
        "created_at": "2023-01-01T00:00:00Z",
        "followers_count": 100,
        "followings_count": 50,
        "posts_count": 30
      }
    }
  }
  ```

- 更新用户信息: `PUT /users/:id`
  ```json
  // 请求头
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

  // 请求体
  {
    "username": "newusername",
    "bio": "新的用户简介",
    "avatar": "base64编码的图片数据"
  }

  // 响应体
  {
    "code": 200,
    "message": "更新成功",
    "data": {
      "user": {
        "id": "user123",
        "username": "newusername",
        "email": "<EMAIL>",
        "avatar": "http://example.com/new-avatar.jpg",
        "bio": "新的用户简介"
      }
    }
  }
  ```

- 获取用户关注列表: `GET /users/:id/followings`
  ```json
  // 请求头
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

  // 请求参数
  page=1&page_size=20

  // 响应体
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "users": [
        {
          "id": "user456",
          "username": "user456",
          "avatar": "http://example.com/avatar456.jpg",
          "is_following": true
        },
        {
          "id": "user789",
          "username": "user789",
          "avatar": "http://example.com/avatar789.jpg",
          "is_following": true
        }
      ],
      "pagination": {
        "total": 50,
        "page": 1,
        "page_size": 20,
        "total_pages": 3
      }
    }
  }
  ```

- 获取用户粉丝列表: `GET /users/:id/followers`
  ```json
  // 请求头
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

  // 请求参数
  page=1&page_size=20

  // 响应体
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "users": [
        {
          "id": "user456",
          "username": "user456",
          "avatar": "http://example.com/avatar456.jpg",
          "is_following": false
        },
        {
          "id": "user789",
          "username": "user789",
          "avatar": "http://example.com/avatar789.jpg",
          "is_following": true
        }
      ],
      "pagination": {
        "total": 100,
        "page": 1,
        "page_size": 20,
        "total_pages": 5
      }
    }
  }
  ```

- 关注用户: `POST /users/:id/follow`
  ```json
  // 请求头
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

  // 响应体
  {
    "code": 200,
    "message": "关注成功",
    "data": {
      "is_following": true
    }
  }
  ```

- 取消关注: `POST /users/:id/unfollow`
  ```json
  // 请求头
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

  // 响应体
  {
    "code": 200,
    "message": "取消关注成功",
    "data": {
      "is_following": false
    }
  }
  ```

### 宠物相关API
- 获取宠物列表: `GET /pets`
- 获取宠物详情: `GET /pets/:id`
- 创建宠物: `POST /pets`
- 更新宠物: `PUT /pets/:id`
- 删除宠物: `DELETE /pets/:id`

### 动态相关API
- 获取动态列表: `GET /posts`
- 获取动态详情: `GET /posts/:id`
- 创建动态: `POST /posts`
- 更新动态: `PUT /posts/:id`
- 删除动态: `DELETE /posts/:id`
- 点赞动态: `POST /posts/:id/like`
- 取消点赞: `POST /posts/:id/unlike`
- 获取评论: `GET /posts/:id/comments`
- 发表评论: `POST /posts/:id/comments`

### 交易相关API
- 获取商品列表: `GET /listings`
- 获取商品详情: `GET /listings/:id`
- 创建商品: `POST /listings`
- 更新商品: `PUT /listings/:id`
- 删除商品: `DELETE /listings/:id`
- 变更商品状态: `PUT /listings/:id/status`
- 搜索商品: `GET /listings/search`

### 社区相关API
- 获取活动列表: `GET /community/events`
- 获取活动详情: `GET /community/events/:id`
- 获取小组列表: `GET /community/groups`
- 获取小组详情: `GET /community/groups/:id`
- 获取讨论列表: `GET /community/discussions`
- 获取讨论详情: `GET /community/discussions/:id`

### 聊天相关API
- 获取会话列表: `GET /chat/conversations`
- 获取会话详情: `GET /chat/conversations/:id`
- 创建会话: `POST /chat/conversations`
- 获取消息列表: `GET /chat/conversations/:id/messages`
- 发送消息: `POST /chat/messages`
- WebSocket连接: `WS /chat/ws`

### 搜索相关API
- 全局搜索: `GET /search`
- 获取搜索建议: `GET /search/suggestions`
- 获取热门搜索: `GET /search/hot-keywords`

## 认证与授权

后端API使用JWT（JSON Web Token）进行认证。认证流程如下：

1. 用户登录成功后，服务器返回`access_token`和`refresh_token`
2. 客户端将`access_token`存储在内存中，将`refresh_token`安全存储在Keychain中
3. 每次请求API时，在HTTP请求头中添加`Authorization: Bearer {access_token}`
4. 当`access_token`过期时，使用`refresh_token`获取新的`access_token`
5. 如果`refresh_token`也过期，则需要用户重新登录

## Swift代码示例

### 网络层实现

```swift
// APIClient.swift
import Foundation

enum APIError: Error {
    case invalidURL
    case requestFailed(Error)
    case invalidResponse
    case decodingFailed(Error)
    case serverError(code: Int, message: String)
    case unauthorized
    case unknown
}

class APIClient {
    static let shared = APIClient()

    private let baseURL = "http://your-server-address/api/v1"
    private let session = URLSession.shared

    private init() {}

    func request<T: Decodable>(
        endpoint: String,
        method: String = "GET",
        parameters: [String: Any]? = nil,
        headers: [String: String]? = nil
    ) async throws -> T {
        guard let url = URL(string: baseURL + endpoint) else {
            throw APIError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = method

        // 添加默认头部
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        // 添加自定义头部
        headers?.forEach { key, value in
            request.addValue(value, forHTTPHeaderField: key)
        }

        // 添加认证Token
        if let token = TokenManager.shared.accessToken {
            request.addValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }

        // 添加请求参数
        if let parameters = parameters {
            if method == "GET" {
                // 对于GET请求，将参数添加到URL
                var components = URLComponents(url: url, resolvingAgainstBaseURL: false)!
                components.queryItems = parameters.map { key, value in
                    URLQueryItem(name: key, string: "\(value)")
                }
                request.url = components.url
            } else {
                // 对于其他请求，将参数添加到请求体
                request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
            }
        }

        do {
            let (data, response) = try await session.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }

            // 处理HTTP状态码
            switch httpResponse.statusCode {
            case 200...299:
                // 成功
                break
            case 401:
                // 未授权，尝试刷新Token
                throw APIError.unauthorized
            case 400...499:
                // 客户端错误
                let errorResponse = try JSONDecoder().decode(ErrorResponse.self, from: data)
                throw APIError.serverError(code: httpResponse.statusCode, message: errorResponse.message)
            case 500...599:
                // 服务器错误
                let errorResponse = try JSONDecoder().decode(ErrorResponse.self, from: data)
                throw APIError.serverError(code: httpResponse.statusCode, message: errorResponse.message)
            default:
                throw APIError.unknown
            }

            // 解码响应数据
            let decoder = JSONDecoder()
            decoder.keyDecodingStrategy = .convertFromSnakeCase

            do {
                return try decoder.decode(T.self, from: data)
            } catch {
                throw APIError.decodingFailed(error)
            }
        } catch {
            if let apiError = error as? APIError {
                throw apiError
            }
            throw APIError.requestFailed(error)
        }
    }
}

// 响应模型
struct APIResponse<T: Decodable>: Decodable {
    let code: Int
    let message: String
    let data: T
}

struct ErrorResponse: Decodable {
    let code: Int
    let message: String
}

// Token管理
class TokenManager {
    static let shared = TokenManager()

    private let keychainService = "com.yourapp.auth"
    private let accessTokenKey = "accessToken"
    private let refreshTokenKey = "refreshToken"

    private init() {}

    var accessToken: String? {
        get {
            return UserDefaults.standard.string(forKey: accessTokenKey)
        }
        set {
            UserDefaults.standard.set(newValue, forKey: accessTokenKey)
        }
    }

    var refreshToken: String? {
        get {
            return KeychainWrapper.standard.string(forKey: refreshTokenKey, service: keychainService)
        }
        set {
            if let token = newValue {
                KeychainWrapper.standard.set(token, forKey: refreshTokenKey, service: keychainService)
            } else {
                KeychainWrapper.standard.removeObject(forKey: refreshTokenKey, service: keychainService)
            }
        }
    }

    func saveTokens(accessToken: String, refreshToken: String) {
        self.accessToken = accessToken
        self.refreshToken = refreshToken
    }

    func clearTokens() {
        accessToken = nil
        refreshToken = nil
    }
}
```

### 用户认证实现

```swift
// AuthService.swift
import Foundation

class AuthService {
    static let shared = AuthService()

    private init() {}

    func login(email: String, password: String) async throws -> User {
        let parameters = [
            "email": email,
            "password": password
        ]

        let response: APIResponse<LoginResponse> = try await APIClient.shared.request(
            endpoint: "/users/login",
            method: "POST",
            parameters: parameters
        )

        // 保存Token
        TokenManager.shared.saveTokens(
            accessToken: response.data.token.accessToken,
            refreshToken: response.data.token.refreshToken
        )

        return response.data.user
    }

    func register(username: String, email: String, password: String, confirmPassword: String) async throws -> User {
        let parameters = [
            "username": username,
            "email": email,
            "password": password,
            "confirm_password": confirmPassword
        ]

        let response: APIResponse<RegisterResponse> = try await APIClient.shared.request(
            endpoint: "/users/register",
            method: "POST",
            parameters: parameters
        )

        return response.data.user
    }

    func refreshToken() async throws -> TokenResponse {
        guard let refreshToken = TokenManager.shared.refreshToken else {
            throw APIError.unauthorized
        }

        let parameters = [
            "refresh_token": refreshToken
        ]

        let response: APIResponse<RefreshTokenResponse> = try await APIClient.shared.request(
            endpoint: "/users/refresh-token",
            method: "POST",
            parameters: parameters
        )

        // 保存新Token
        TokenManager.shared.saveTokens(
            accessToken: response.data.token.accessToken,
            refreshToken: response.data.token.refreshToken
        )

        return response.data.token
    }

    func logout() async throws {
        let _: APIResponse<EmptyResponse> = try await APIClient.shared.request(
            endpoint: "/users/logout",
            method: "POST"
        )

        // 清除Token
        TokenManager.shared.clearTokens()
    }
}

// 响应模型
struct LoginResponse: Decodable {
    let user: User
    let token: TokenResponse
}

struct RegisterResponse: Decodable {
    let user: User
}

struct RefreshTokenResponse: Decodable {
    let token: TokenResponse
}

struct TokenResponse: Decodable {
    let accessToken: String
    let refreshToken: String
    let expiresIn: Int
}

struct EmptyResponse: Decodable {}

struct User: Decodable {
    let id: String
    let username: String
    let email: String
    let avatar: String?
    let bio: String?
    let createdAt: Date?
    let followersCount: Int?
    let followingsCount: Int?
    let postsCount: Int?
}
```

## 常见问题

### 1. 如何处理网络请求错误？
实现统一的错误处理机制，根据后端返回的错误码显示相应的错误信息。

```swift
func handleAPIError(_ error: Error) {
    if let apiError = error as? APIError {
        switch apiError {
        case .unauthorized:
            // 显示登录界面
            showLoginScreen()
        case .serverError(let code, let message):
            // 显示错误消息
            showAlert(title: "错误 \(code)", message: message)
        case .invalidURL:
            showAlert(title: "错误", message: "无效的URL")
        case .requestFailed(let error):
            showAlert(title: "请求失败", message: error.localizedDescription)
        case .invalidResponse:
            showAlert(title: "错误", message: "无效的响应")
        case .decodingFailed(let error):
            showAlert(title: "解析错误", message: error.localizedDescription)
        case .unknown:
            showAlert(title: "未知错误", message: "发生未知错误")
        }
    } else {
        showAlert(title: "错误", message: error.localizedDescription)
    }
}
```

### 2. 如何处理图片和视频上传？
使用分块上传方式，支持断点续传，并显示上传进度。

```swift
func uploadImage(image: UIImage, progressHandler: @escaping (Double) -> Void) async throws -> String {
    guard let imageData = image.jpegData(compressionQuality: 0.8) else {
        throw UploadError.invalidData
    }

    let boundary = UUID().uuidString
    let url = URL(string: baseURL + "/upload/image")!

    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")

    if let token = TokenManager.shared.accessToken {
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
    }

    let formData = createFormData(with: imageData, boundary: boundary, fileName: "image.jpg", mimeType: "image/jpeg")

    let (data, response) = try await URLSession.shared.upload(
        for: request,
        from: formData,
        delegate: UploadProgressDelegate(progressHandler: progressHandler)
    )

    guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
        throw UploadError.serverError
    }

    let decoder = JSONDecoder()
    let uploadResponse = try decoder.decode(UploadResponse.self, from: data)

    return uploadResponse.data.url
}
```

### 3. 如何优化应用性能？
- 实现图片缓存
- 使用分页加载数据
- 优化网络请求
- 减少主线程阻塞

```swift
// 图片缓存实现
class ImageCache {
    static let shared = ImageCache()

    private let cache = NSCache<NSString, UIImage>()

    private init() {
        cache.countLimit = 100
    }

    func setImage(_ image: UIImage, forKey key: String) {
        cache.setObject(image, forKey: key as NSString)
    }

    func image(forKey key: String) -> UIImage? {
        return cache.object(forKey: key as NSString)
    }

    func removeImage(forKey key: String) {
        cache.removeObject(forKey: key as NSString)
    }

    func clearCache() {
        cache.removeAllObjects()
    }
}

// 异步加载图片
func loadImage(from urlString: String) async -> UIImage? {
    // 检查缓存
    if let cachedImage = ImageCache.shared.image(forKey: urlString) {
        return cachedImage
    }

    // 从网络加载
    guard let url = URL(string: urlString) else {
        return nil
    }

    do {
        let (data, _) = try await URLSession.shared.data(from: url)
        if let image = UIImage(data: data) {
            // 存入缓存
            ImageCache.shared.setImage(image, forKey: urlString)
            return image
        }
    } catch {
        print("加载图片失败: \(error)")
    }

    return nil
}
```

### 4. 如何保证数据安全？
- 敏感数据使用Keychain存储
- 网络请求使用HTTPS
- 实现证书固定（Certificate Pinning）
- 实现应用内数据加密

```swift
// 证书固定实现
class SecureURLSessionDelegate: NSObject, URLSessionDelegate {
    func urlSession(_ session: URLSession, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
        guard let serverTrust = challenge.protectionSpace.serverTrust,
              challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust else {
            completionHandler(.performDefaultHandling, nil)
            return
        }

        // 获取证书
        let certificate = SecTrustGetCertificateAtIndex(serverTrust, 0)

        // 获取本地证书数据
        guard let localCertificateURL = Bundle.main.url(forResource: "server-certificate", withExtension: "der"),
              let localCertificateData = try? Data(contentsOf: localCertificateURL) else {
            completionHandler(.cancelAuthenticationChallenge, nil)
            return
        }

        // 获取服务器证书数据
        let serverCertificateData = SecCertificateCopyData(certificate!) as Data

        // 比较证书
        if serverCertificateData == localCertificateData {
            let credential = URLCredential(trust: serverTrust)
            completionHandler(.useCredential, credential)
        } else {
            completionHandler(.cancelAuthenticationChallenge, nil)
        }
    }
}
```

### 5. 如何处理WebSocket连接断开？
实现自动重连机制，并在重连成功后同步离线消息。

```swift
class ChatService {
    private var webSocket: URLSessionWebSocketTask?
    private var isConnected = false
    private var reconnectTimer: Timer?
    private var reconnectAttempts = 0
    private let maxReconnectAttempts = 5

    func connect() {
        guard !isConnected else { return }

        let url = URL(string: "ws://your-server-address/chat/ws")!
        var request = URLRequest(url: url)

        if let token = TokenManager.shared.accessToken {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }

        let session = URLSession(configuration: .default)
        webSocket = session.webSocketTask(with: request)

        webSocket?.resume()
        isConnected = true
        reconnectAttempts = 0

        receiveMessage()
    }

    func disconnect() {
        webSocket?.cancel(with: .normalClosure, reason: nil)
        isConnected = false
        stopReconnectTimer()
    }

    private func receiveMessage() {
        webSocket?.receive { [weak self] result in
            guard let self = self else { return }

            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    self.handleMessage(text)
                case .data(let data):
                    if let text = String(data: data, encoding: .utf8) {
                        self.handleMessage(text)
                    }
                @unknown default:
                    break
                }

                // 继续接收消息
                self.receiveMessage()

            case .failure(let error):
                print("WebSocket接收消息失败: \(error)")
                self.isConnected = false
                self.startReconnectTimer()
            }
        }
    }

    private func handleMessage(_ message: String) {
        // 处理接收到的消息
        print("收到消息: \(message)")
    }

    private func startReconnectTimer() {
        stopReconnectTimer()

        guard reconnectAttempts < maxReconnectAttempts else {
            print("达到最大重连次数")
            return
        }

        reconnectTimer = Timer.scheduledTimer(withTimeInterval: Double(reconnectAttempts + 1), repeats: false) { [weak self] _ in
            guard let self = self else { return }

            self.reconnectAttempts += 1
            print("尝试重连 (尝试 \(self.reconnectAttempts)/\(self.maxReconnectAttempts))")
            self.connect()
        }
    }

    private func stopReconnectTimer() {
        reconnectTimer?.invalidate()
        reconnectTimer = nil
    }

    func sendMessage(_ message: String) {
        guard isConnected else {
            print("WebSocket未连接")
            return
        }

        webSocket?.send(.string(message)) { error in
            if let error = error {
                print("发送消息失败: \(error)")
            }
        }
    }
}
```
