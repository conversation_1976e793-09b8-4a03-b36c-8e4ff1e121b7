# 毛伙伴应用技术架构设计

## 目录
1. [架构概述](#架构概述)
2. [技术选型](#技术选型)
3. [应用架构](#应用架构)
4. [数据流设计](#数据流设计)
5. [网络层设计](#网络层设计)
6. [存储层设计](#存储层设计)
7. [UI架构](#UI架构)
8. [安全设计](#安全设计)
9. [性能优化](#性能优化)
10. [测试策略](#测试策略)

## 架构概述

毛伙伴iOS应用采用MVVM+Coordinator架构模式，结合Clean Architecture的思想，将应用分为表现层、领域层和数据层，实现关注点分离和高内聚低耦合的设计目标。

### 架构目标
- **可测试性**：便于编写单元测试和UI测试
- **可维护性**：代码结构清晰，易于理解和修改
- **可扩展性**：便于添加新功能和修改现有功能
- **可重用性**：组件可在不同模块中重用
- **可靠性**：稳定运行，错误处理完善

### 架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        表现层 (Presentation)                  │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │    View     │◄───│  ViewModel  │◄───│ Coordinator │      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
└───────────────────────────┬─────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────┐
│                        领域层 (Domain)                        │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │   Entities  │◄───│   UseCases  │◄───│ Repositories│      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
└───────────────────────────┬─────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────┐
│                        数据层 (Data)                          │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐      │
│  │ API Clients │    │ Repositories│    │Local Storage│      │
│  └─────────────┘    └─────────────┘    └─────────────┘      │
└─────────────────────────────────────────────────────────────┘
```

## 技术选型

### 开发语言与工具
- **编程语言**：Swift 5.0+
- **最低iOS版本**：iOS 14.0
- **开发工具**：Xcode 14.0+
- **包管理**：Swift Package Manager (SPM)
- **版本控制**：Git

### 第三方库
- **网络请求**：Alamofire
- **响应式编程**：Combine
- **JSON解析**：Swift标准库 (Codable)
- **图片加载**：Kingfisher
- **数据库**：CoreData / Realm
- **依赖注入**：自定义DI容器
- **日志记录**：SwiftyBeaver
- **分析工具**：Firebase Analytics
- **崩溃报告**：Firebase Crashlytics
- **UI组件**：自定义组件 + SwiftUI
- **测试框架**：XCTest

## 应用架构

### MVVM+Coordinator模式

#### View
- 负责UI展示和用户交互
- 将用户操作传递给ViewModel
- 通过数据绑定显示ViewModel的状态

#### ViewModel
- 处理业务逻辑
- 调用UseCase执行业务操作
- 维护View的状态
- 不包含UI相关代码

#### Coordinator
- 负责导航和流程控制
- 创建和配置ViewController
- 处理页面之间的跳转
- 管理依赖注入

### 模块化设计

应用按功能模块划分，每个模块包含自己的View、ViewModel、Model等组件：

```
App/
├── Core/                  # 核心组件
│   ├── DI/                # 依赖注入
│   ├── Navigation/        # 导航系统
│   ├── Network/           # 网络层
│   └── Storage/           # 存储层
├── Features/              # 功能模块
│   ├── Auth/              # 认证模块
│   ├── Profile/           # 个人资料模块
│   ├── Pet/               # 宠物模块
│   ├── Post/              # 动态模块
│   ├── Trade/             # 交易模块
│   ├── Community/         # 社区模块
│   ├── Chat/              # 聊天模块
│   └── Search/            # 搜索模块
└── Common/                # 通用组件
    ├── Extensions/        # Swift扩展
    ├── UI/                # UI组件
    └── Utils/             # 工具类
```

## 数据流设计

### 单向数据流
采用单向数据流模式，确保数据流动清晰可预测：

1. **View** 发送用户操作事件给 **ViewModel**
2. **ViewModel** 调用 **UseCase** 处理业务逻辑
3. **UseCase** 通过 **Repository** 获取或修改数据
4. **Repository** 从 **API** 或 **本地存储** 获取数据
5. 数据按相反方向流回 **View**，更新UI

### 状态管理
- 使用Combine框架实现响应式状态管理
- ViewModel通过发布者(Publisher)发布状态变化
- View通过订阅(Subscriber)接收状态更新

```swift
class PostViewModel {
    // 输出状态
    @Published var posts: [Post] = []
    @Published var isLoading = false
    @Published var error: Error? = nil
    
    // 输入事件
    func loadPosts() {
        isLoading = true
        postUseCase.getPosts()
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.error = error
                    }
                },
                receiveValue: { [weak self] posts in
                    self?.posts = posts
                }
            )
            .store(in: &cancellables)
    }
}
```

## 网络层设计

### API客户端
- 基于Alamofire构建网络请求层
- 实现请求拦截器，添加认证Token
- 实现响应处理器，统一处理错误

```swift
protocol APIClientProtocol {
    func request<T: Decodable>(
        endpoint: APIEndpoint,
        method: HTTPMethod,
        parameters: Parameters?,
        headers: HTTPHeaders?
    ) -> AnyPublisher<T, Error>
}

class APIClient: APIClientProtocol {
    private let session: Session
    
    init(interceptor: RequestInterceptor? = nil) {
        self.session = Session(interceptor: interceptor)
    }
    
    func request<T: Decodable>(
        endpoint: APIEndpoint,
        method: HTTPMethod = .get,
        parameters: Parameters? = nil,
        headers: HTTPHeaders? = nil
    ) -> AnyPublisher<T, Error> {
        // 实现请求逻辑
    }
}
```

### 认证拦截器
```swift
class AuthInterceptor: RequestInterceptor {
    func adapt(_ urlRequest: URLRequest, for session: Session, completion: @escaping (Result<URLRequest, Error>) -> Void) {
        var request = urlRequest
        if let token = TokenManager.shared.accessToken {
            request.headers.add(.authorization(bearerToken: token))
        }
        completion(.success(request))
    }
    
    func retry(_ request: Request, for session: Session, dueTo error: Error, completion: @escaping (RetryResult) -> Void) {
        // 处理401错误，刷新Token并重试
    }
}
```

### 错误处理
```swift
enum APIError: Error {
    case invalidURL
    case requestFailed(Error)
    case invalidResponse
    case decodingFailed(Error)
    case serverError(code: Int, message: String)
    case unauthorized
    case unknown
}

class APIErrorHandler {
    static func handle(_ error: Error) -> APIError {
        // 将各种错误转换为APIError
    }
}
```

## 存储层设计

### 本地存储策略
- **用户数据**：UserDefaults（非敏感）+ Keychain（敏感）
- **缓存数据**：CoreData / Realm
- **文件存储**：FileManager
- **图片缓存**：Kingfisher

### 数据持久化
```swift
protocol LocalStorageProtocol {
    func save<T: Encodable>(_ object: T, forKey key: String) throws
    func load<T: Decodable>(forKey key: String) throws -> T?
    func delete(forKey key: String) throws
}

class LocalStorage: LocalStorageProtocol {
    // 实现存储逻辑
}
```

### 缓存管理
```swift
class CacheManager {
    static let shared = CacheManager()
    
    private let cache = NSCache<NSString, AnyObject>()
    
    func setObject(_ object: AnyObject, forKey key: String) {
        cache.setObject(object, forKey: key as NSString)
    }
    
    func object(forKey key: String) -> AnyObject? {
        return cache.object(forKey: key as NSString)
    }
    
    func removeObject(forKey key: String) {
        cache.removeObject(forKey: key as NSString)
    }
    
    func clearCache() {
        cache.removeAllObjects()
    }
}
```

## UI架构

### 组件化UI设计
- 构建可重用的UI组件库
- 组件支持自定义主题和样式
- 使用组合模式构建复杂界面

```swift
// 基础组件
struct MHButton: View {
    enum Style {
        case primary, secondary, outline
    }
    
    let title: String
    let style: Style
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 16, weight: .medium))
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(backgroundColor)
                .foregroundColor(foregroundColor)
                .cornerRadius(8)
        }
    }
    
    // 根据样式计算背景色和前景色
    private var backgroundColor: Color { ... }
    private var foregroundColor: Color { ... }
}
```

### 主题系统
```swift
struct MHTheme {
    let primaryColor: UIColor
    let secondaryColor: UIColor
    let backgroundColor: UIColor
    let textColor: UIColor
    let accentColor: UIColor
    
    static let light = MHTheme(
        primaryColor: UIColor(hex: "#FF5A5F"),
        secondaryColor: UIColor(hex: "#00A699"),
        backgroundColor: UIColor.white,
        textColor: UIColor(hex: "#484848"),
        accentColor: UIColor(hex: "#FF5A5F")
    )
    
    static let dark = MHTheme(
        primaryColor: UIColor(hex: "#FF5A5F"),
        secondaryColor: UIColor(hex: "#00A699"),
        backgroundColor: UIColor(hex: "#121212"),
        textColor: UIColor.white,
        accentColor: UIColor(hex: "#FF5A5F")
    )
}

class ThemeManager {
    static let shared = ThemeManager()
    
    @Published var currentTheme: MHTheme = .light
    
    func applyTheme(_ theme: MHTheme) {
        currentTheme = theme
        // 应用主题到UI组件
    }
}
```

## 安全设计

### 数据安全
- 敏感数据使用Keychain存储
- 网络传输使用HTTPS
- 实现证书固定（Certificate Pinning）
- 本地数据加密存储

### 认证安全
- 使用JWT进行API认证
- 实现Token自动刷新机制
- 生物识别认证（Touch ID / Face ID）
- 防止重放攻击

### 代码安全
- 混淆敏感代码
- 防止注入攻击
- 防止调试和逆向工程

## 性能优化

### 图片优化
- 图片缓存和预加载
- 图片尺寸优化
- 图片压缩和格式选择

### 网络优化
- 请求合并和批处理
- 数据压缩
- 缓存控制
- 断点续传

### UI性能
- 异步渲染
- 视图重用
- 减少主线程阻塞
- 优化动画性能

## 测试策略

### 单元测试
- 使用XCTest框架
- 测试业务逻辑和数据处理
- 使用依赖注入便于测试
- 目标覆盖率80%

### UI测试
- 使用XCUITest
- 测试关键用户流程
- 测试不同设备和系统版本

### 集成测试
- 测试模块间交互
- 测试与后端API集成
- 测试第三方服务集成

### 性能测试
- 使用Instruments工具
- 测试内存使用
- 测试CPU使用
- 测试启动时间和响应时间
