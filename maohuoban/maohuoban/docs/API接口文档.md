# 毛伙伴应用API接口文档

## 目录
1. [接口规范](#接口规范)
2. [认证相关API](#认证相关API)
3. [用户相关API](#用户相关API)
4. [宠物相关API](#宠物相关API)
5. [动态相关API](#动态相关API)
6. [交易相关API](#交易相关API)
7. [社区相关API](#社区相关API)
8. [聊天相关API](#聊天相关API)
9. [搜索相关API](#搜索相关API)
10. [错误码说明](#错误码说明)

## 接口规范

### 基础URL
```
http://api.maohuoban.com/v1
```

### 请求格式
- GET请求：参数通过URL查询字符串传递
- POST/PUT/DELETE请求：参数通过JSON格式在请求体中传递
- 请求头必须包含`Content-Type: application/json`

### 认证方式
- 使用Bearer Token认证
- 在请求头中添加`Authorization: Bearer {access_token}`

### 响应格式
所有API响应均为JSON格式，包含以下字段：
```json
{
  "code": 200,           // 状态码，200表示成功
  "message": "成功",      // 状态描述
  "data": {              // 响应数据，根据接口不同而不同
    // 具体数据
  }
}
```

### 分页参数
支持分页的接口使用以下查询参数：
- `page`: 页码，从1开始
- `page_size`: 每页记录数，默认20，最大100

分页响应格式：
```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "items": [
      // 列表数据
    ],
    "pagination": {
      "total": 100,       // 总记录数
      "page": 1,          // 当前页码
      "page_size": 20,    // 每页记录数
      "total_pages": 5    // 总页数
    }
  }
}
```

## 认证相关API

### 登录
- **URL**: `/users/login`
- **方法**: `POST`
- **描述**: 用户登录并获取访问令牌
- **请求参数**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "user": {
        "id": "user123",
        "username": "username",
        "email": "<EMAIL>",
        "avatar": "http://example.com/avatar.jpg"
      },
      "token": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 3600
      }
    }
  }
  ```

### 注册
- **URL**: `/users/register`
- **方法**: `POST`
- **描述**: 注册新用户
- **请求参数**:
  ```json
  {
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirm_password": "password123"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "注册成功",
    "data": {
      "user": {
        "id": "user456",
        "username": "newuser",
        "email": "<EMAIL>"
      }
    }
  }
  ```

### 刷新Token
- **URL**: `/users/refresh-token`
- **方法**: `POST`
- **描述**: 使用刷新令牌获取新的访问令牌
- **请求参数**:
  ```json
  {
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "刷新成功",
    "data": {
      "token": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expires_in": 3600
      }
    }
  }
  ```

### 登出
- **URL**: `/users/logout`
- **方法**: `POST`
- **描述**: 用户登出，使当前令牌失效
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "登出成功"
  }
  ```

## 用户相关API

### 获取用户信息
- **URL**: `/users/:id`
- **方法**: `GET`
- **描述**: 获取指定用户的详细信息
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "user": {
        "id": "user123",
        "username": "username",
        "email": "<EMAIL>",
        "avatar": "http://example.com/avatar.jpg",
        "bio": "用户简介",
        "created_at": "2023-01-01T00:00:00Z",
        "followers_count": 100,
        "followings_count": 50,
        "posts_count": 30
      }
    }
  }
  ```

### 更新用户信息
- **URL**: `/users/:id`
- **方法**: `PUT`
- **描述**: 更新用户信息
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **请求参数**:
  ```json
  {
    "username": "newusername",
    "bio": "新的用户简介",
    "avatar": "base64编码的图片数据"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "更新成功",
    "data": {
      "user": {
        "id": "user123",
        "username": "newusername",
        "email": "<EMAIL>",
        "avatar": "http://example.com/new-avatar.jpg",
        "bio": "新的用户简介"
      }
    }
  }
  ```

### 获取用户关注列表
- **URL**: `/users/:id/followings`
- **方法**: `GET`
- **描述**: 获取用户关注的人列表
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **请求参数**:
  ```
  page=1&page_size=20
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "items": [
        {
          "id": "user456",
          "username": "user456",
          "avatar": "http://example.com/avatar456.jpg",
          "is_following": true
        },
        {
          "id": "user789",
          "username": "user789",
          "avatar": "http://example.com/avatar789.jpg",
          "is_following": true
        }
      ],
      "pagination": {
        "total": 50,
        "page": 1,
        "page_size": 20,
        "total_pages": 3
      }
    }
  }
  ```

### 获取用户粉丝列表
- **URL**: `/users/:id/followers`
- **方法**: `GET`
- **描述**: 获取用户的粉丝列表
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **请求参数**:
  ```
  page=1&page_size=20
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "items": [
        {
          "id": "user456",
          "username": "user456",
          "avatar": "http://example.com/avatar456.jpg",
          "is_following": false
        },
        {
          "id": "user789",
          "username": "user789",
          "avatar": "http://example.com/avatar789.jpg",
          "is_following": true
        }
      ],
      "pagination": {
        "total": 100,
        "page": 1,
        "page_size": 20,
        "total_pages": 5
      }
    }
  }
  ```

### 关注用户
- **URL**: `/users/:id/follow`
- **方法**: `POST`
- **描述**: 关注指定用户
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "关注成功",
    "data": {
      "is_following": true
    }
  }
  ```

### 取消关注
- **URL**: `/users/:id/unfollow`
- **方法**: `POST`
- **描述**: 取消关注指定用户
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "取消关注成功",
    "data": {
      "is_following": false
    }
  }
  ```

## 宠物相关API

### 获取宠物列表
- **URL**: `/pets`
- **方法**: `GET`
- **描述**: 获取当前用户的宠物列表
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "items": [
        {
          "id": "pet123",
          "name": "小花",
          "type": "猫",
          "breed": "英短",
          "age": 2,
          "gender": "female",
          "avatar": "http://example.com/pet123.jpg",
          "created_at": "2023-01-01T00:00:00Z"
        },
        {
          "id": "pet456",
          "name": "大黄",
          "type": "狗",
          "breed": "金毛",
          "age": 3,
          "gender": "male",
          "avatar": "http://example.com/pet456.jpg",
          "created_at": "2023-02-01T00:00:00Z"
        }
      ]
    }
  }
  ```

### 获取宠物详情
- **URL**: `/pets/:id`
- **方法**: `GET`
- **描述**: 获取指定宠物的详细信息
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "获取成功",
    "data": {
      "pet": {
        "id": "pet123",
        "name": "小花",
        "type": "猫",
        "breed": "英短",
        "age": 2,
        "gender": "female",
        "avatar": "http://example.com/pet123.jpg",
        "description": "一只可爱的英短猫",
        "birthday": "2021-01-15",
        "weight": 4.5,
        "owner": {
          "id": "user123",
          "username": "username",
          "avatar": "http://example.com/avatar.jpg"
        },
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-03-01T00:00:00Z"
      }
    }
  }
  ```

### 创建宠物
- **URL**: `/pets`
- **方法**: `POST`
- **描述**: 创建新宠物
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **请求参数**:
  ```json
  {
    "name": "小黑",
    "type": "猫",
    "breed": "美短",
    "gender": "male",
    "birthday": "2022-05-10",
    "weight": 3.8,
    "description": "一只活泼的美短猫",
    "avatar": "base64编码的图片数据"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "创建成功",
    "data": {
      "pet": {
        "id": "pet789",
        "name": "小黑",
        "type": "猫",
        "breed": "美短",
        "age": 1,
        "gender": "male",
        "avatar": "http://example.com/pet789.jpg",
        "description": "一只活泼的美短猫",
        "birthday": "2022-05-10",
        "weight": 3.8,
        "created_at": "2023-05-15T00:00:00Z"
      }
    }
  }
  ```

### 更新宠物
- **URL**: `/pets/:id`
- **方法**: `PUT`
- **描述**: 更新宠物信息
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **请求参数**:
  ```json
  {
    "name": "小黑黑",
    "weight": 4.2,
    "description": "一只更活泼的美短猫"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "更新成功",
    "data": {
      "pet": {
        "id": "pet789",
        "name": "小黑黑",
        "type": "猫",
        "breed": "美短",
        "age": 1,
        "gender": "male",
        "avatar": "http://example.com/pet789.jpg",
        "description": "一只更活泼的美短猫",
        "birthday": "2022-05-10",
        "weight": 4.2,
        "updated_at": "2023-05-16T00:00:00Z"
      }
    }
  }
  ```

### 删除宠物
- **URL**: `/pets/:id`
- **方法**: `DELETE`
- **描述**: 删除宠物
- **请求头**:
  ```
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "删除成功"
  }
  ```

## 错误码说明

| 错误码 | 描述 | 说明 |
|--------|------|------|
| 200 | 成功 | 请求成功处理 |
| 400 | 请求错误 | 请求参数有误 |
| 401 | 未授权 | 未提供认证信息或认证失败 |
| 403 | 禁止访问 | 没有权限访问资源 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 409 | 资源冲突 | 资源状态冲突，如重复创建 |
| 422 | 参数验证失败 | 请求参数验证失败 |
| 429 | 请求过多 | 超出API调用频率限制 |
| 500 | 服务器错误 | 服务器内部错误 |
| 503 | 服务不可用 | 服务暂时不可用 |
