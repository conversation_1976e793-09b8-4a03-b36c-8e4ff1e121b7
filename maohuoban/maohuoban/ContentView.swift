//
//  ContentView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//

import SwiftUI

struct ContentView: View {
    // 状态变量
    @State private var isShowingLogin = false
    @State private var isLoggedIn = false

    // 检查用户是否已登录
    private func checkLoginStatus() {
        // 这里可以添加检查用户登录状态的逻辑
        // 例如，从 UserDefaults 或其他存储中读取登录状态
        // 暂时使用模拟数据
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // 模拟自动登录，实际应用中应该检查用户凭证
            // isLoggedIn = true
        }
    }

    var body: some View {
        Group {
            if isLoggedIn {
                // 如果用户已登录，显示首页
                HomeView()
            } else {
                // 如果用户未登录，显示欢迎页面
                NavigationView {
                    VStack(spacing: 20) {
                        // 标题
                        Text("毛伙伴")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8)) // 使用疗愈蓝

                        Text("宠物社交平台")
                            .font(.subheadline)
                            .foregroundColor(.gray)

                        Spacer()

                        // 图标
                        Image(systemName: "pawprint.fill")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 100, height: 100)
                            .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8)) // 使用疗愈蓝

                        Spacer()

                        // 按钮
                        Button(action: {
                            // 显示登录界面
                            isShowingLogin = true
                        }) {
                            Text("登录/注册")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding()
                                .frame(maxWidth: .infinity)
                                .background(Color(red: 0.0, green: 0.48, blue: 0.8)) // 使用疗愈蓝
                                .cornerRadius(10)
                        }
                        .padding(.horizontal, 50)

                        // 游客模式按钮
                        Button(action: {
                            // 直接进入首页，不需要登录
                            isLoggedIn = true
                        }) {
                            Text("以游客身份浏览")
                                .font(.subheadline)
                                .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8)) // 使用疗愈蓝
                        }
                        .padding(.top, 10)

                        // 版本信息
                        Text("版本 1.0.0")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.top, 20)
                    }
                    .padding()
                    .navigationBarHidden(true)
                    .sheet(isPresented: $isShowingLogin) {
                        LoginView(isLoggedIn: $isLoggedIn)
                    }
                    .onAppear {
                        checkLoginStatus()
                    }
                }
            }
        }
    }
}

// 登录视图
struct LoginView: View {
    @State private var username = ""
    @State private var password = ""
    @Environment(\.presentationMode) var presentationMode
    @Binding var isLoggedIn: Bool

    init(isLoggedIn: Binding<Bool>) {
        self._isLoggedIn = isLoggedIn
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("登录")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                // 用户名输入框
                TextField("用户名", text: $username)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)

                // 密码输入框
                SecureField("密码", text: $password)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)

                // 登录按钮
                Button(action: {
                    // 处理登录逻辑
                    login()
                }) {
                    Text("登录")
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(Color(red: 0.0, green: 0.48, blue: 0.8)) // 使用疗愈蓝
                        .cornerRadius(10)
                }

                // 注册链接
                NavigationLink(destination: RegisterView(isLoggedIn: $isLoggedIn)) {
                    Text("没有账号？立即注册")
                        .foregroundColor(Color(red: 0.0, green: 0.48, blue: 0.8)) // 使用疗愈蓝
                }

                Spacer()
            }
            .padding()
            .navigationBarItems(leading: Button(action: {
                presentationMode.wrappedValue.dismiss()
            }) {
                Image(systemName: "xmark")
                    .foregroundColor(.gray)
            })
        }
    }

    // 登录方法
    private func login() {
        // 这里实现登录逻辑
        print("登录: \(username), \(password)")

        // 模拟登录成功
        if !username.isEmpty && !password.isEmpty {
            isLoggedIn = true
            presentationMode.wrappedValue.dismiss()
        }
    }
}

// 注册视图
struct RegisterView: View {
    @State private var username = ""
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @Binding var isLoggedIn: Bool
    @Environment(\.presentationMode) var presentationMode

    init(isLoggedIn: Binding<Bool>) {
        self._isLoggedIn = isLoggedIn
    }

    var body: some View {
        VStack(spacing: 20) {
            // 标题
            Text("注册")
                .font(.largeTitle)
                .fontWeight(.bold)

            // 用户名输入框
            TextField("用户名", text: $username)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

            // 邮箱输入框
            TextField("邮箱", text: $email)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

            // 密码输入框
            SecureField("密码", text: $password)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

            // 确认密码输入框
            SecureField("确认密码", text: $confirmPassword)
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(10)

            // 注册按钮
            Button(action: {
                // 处理注册逻辑
                register()
            }) {
                Text("注册")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color(red: 0.0, green: 0.48, blue: 0.8)) // 使用疗愈蓝
                    .cornerRadius(10)
            }

            Spacer()
        }
        .padding()
        .navigationBarTitle("注册", displayMode: .inline)
    }

    // 注册方法
    private func register() {
        // 这里实现注册逻辑
        print("注册: \(username), \(email), \(password)")

        // 验证输入
        if !username.isEmpty && !email.isEmpty && !password.isEmpty && password == confirmPassword {
            // 模拟注册成功
            isLoggedIn = true
            presentationMode.wrappedValue.dismiss()
        }
    }
}

#Preview {
    ContentView()
}
