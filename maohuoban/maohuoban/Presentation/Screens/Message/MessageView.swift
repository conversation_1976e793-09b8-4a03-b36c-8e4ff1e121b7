//
//  MessageView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：消息中心视图
//  重构说明：从HomeView.swift中分离出来，实现Message模块的独立管理
//  架构模式：MVVM - 这是View层，后续会添加对应的ViewModel
//

import SwiftUI

// MARK: - 消息中心视图
/// 消息中心页面 - 显示聊天会话、系统通知、互动消息等
/// 包含私信聊天、群聊、系统消息、点赞评论通知等功能
/// 支持消息搜索、筛选和管理
struct MessageView: View {
    
    // MARK: - 状态变量
    /// 当前选中的消息类型索引 (0:全部, 1:私信, 2:通知, 3:系统)
    @State private var selectedMessageType = 0
    
    /// 搜索文本
    @State private var searchText = ""
    
    /// 是否显示搜索栏
    @State private var showSearchBar = false
    
    /// 未读消息数量
    @State private var unreadCount = 5
    
    // MARK: - 常量
    /// 消息类型选项数组
    private let messageTypes = ["全部", "私信", "通知", "系统"]
    
    // MARK: - 视图主体
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // MARK: 搜索栏（可选显示）
                if showSearchBar {
                    createSearchBar()
                }
                
                // MARK: 消息类型筛选栏
                createMessageTypeFilter()
                
                // MARK: 消息列表
                ScrollView {
                    LazyVStack(spacing: 0) {
                        // 根据选中的类型显示不同消息
                        switch selectedMessageType {
                        case 0:
                            createAllMessagesContent()
                        case 1:
                            createPrivateMessagesContent()
                        case 2:
                            createNotificationsContent()
                        case 3:
                            createSystemMessagesContent()
                        default:
                            createAllMessagesContent()
                        }
                    }
                }
                .refreshable {
                    // 下拉刷新
                    await refreshMessages()
                }
            }
            .navigationTitle("消息")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showSearchBar.toggle()
                    }) {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(MHTheme.primaryTextColor)
                    }
                }
            }
        }
        .onAppear {
            // 视图出现时的初始化操作
            setupMessageView()
        }
    }
    
    // MARK: - 私有方法
    
    /// 创建搜索栏
    /// - Returns: 搜索栏视图
    private func createSearchBar() -> some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(MHTheme.secondaryTextColor)
            
            TextField("搜索消息", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .font(MHTheme.bodyFont)
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(MHTheme.secondaryTextColor)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(MHTheme.tertiaryBackgroundColor)
        .cornerRadius(MHTheme.cornerRadiusM)
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
    
    /// 创建消息类型筛选栏
    /// - Returns: 筛选栏视图
    private func createMessageTypeFilter() -> some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 20) {
                ForEach(0..<messageTypes.count, id: \.self) { index in
                    Button(action: {
                        selectedMessageType = index
                        
                        // 添加触觉反馈
                        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                        impactFeedback.impactOccurred()
                    }) {
                        VStack(spacing: 4) {
                            HStack(spacing: 4) {
                                Text(messageTypes[index])
                                    .font(.system(size: 16, weight: selectedMessageType == index ? .bold : .medium))
                                    .foregroundColor(selectedMessageType == index ? MHTheme.primaryColor : MHTheme.secondaryTextColor)
                                
                                // 未读消息徽章
                                if index == 1 && unreadCount > 0 {
                                    Text("\(unreadCount)")
                                        .font(.system(size: 12, weight: .bold))
                                        .foregroundColor(.white)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(Color.red)
                                        .clipShape(Capsule())
                                }
                            }
                            
                            // 底部指示器
                            Rectangle()
                                .frame(height: 2)
                                .foregroundColor(selectedMessageType == index ? MHTheme.primaryColor : .clear)
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 8)
        .background(MHTheme.backgroundColor)
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(MHTheme.separatorColor),
            alignment: .bottom
        )
    }
    
    /// 创建全部消息内容
    /// - Returns: 全部消息视图
    private func createAllMessagesContent() -> some View {
        VStack(spacing: 0) {
            // TODO: 实现全部消息内容
            ForEach(1...10, id: \.self) { index in
                createMessageRow(
                    avatar: "person.circle.fill",
                    name: "用户\(index)",
                    lastMessage: "这是最后一条消息的内容...",
                    time: "刚刚",
                    unreadCount: index <= 3 ? index : 0
                )
            }
        }
    }
    
    /// 创建私信消息内容
    /// - Returns: 私信消息视图
    private func createPrivateMessagesContent() -> some View {
        VStack(spacing: 0) {
            // TODO: 实现私信消息内容
            ForEach(1...5, id: \.self) { index in
                createMessageRow(
                    avatar: "person.circle.fill",
                    name: "好友\(index)",
                    lastMessage: "私信内容...",
                    time: "\(index)分钟前",
                    unreadCount: index <= 2 ? index : 0
                )
            }
        }
    }
    
    /// 创建通知消息内容
    /// - Returns: 通知消息视图
    private func createNotificationsContent() -> some View {
        VStack(spacing: 0) {
            // TODO: 实现通知消息内容
            ForEach(1...8, id: \.self) { index in
                createMessageRow(
                    avatar: "bell.circle.fill",
                    name: "互动通知",
                    lastMessage: "有人点赞了你的动态",
                    time: "\(index)小时前",
                    unreadCount: 0
                )
            }
        }
    }
    
    /// 创建系统消息内容
    /// - Returns: 系统消息视图
    private func createSystemMessagesContent() -> some View {
        VStack(spacing: 0) {
            // TODO: 实现系统消息内容
            ForEach(1...3, id: \.self) { index in
                createMessageRow(
                    avatar: "gear.circle.fill",
                    name: "系统消息",
                    lastMessage: "系统通知内容...",
                    time: "\(index)天前",
                    unreadCount: 0
                )
            }
        }
    }
    
    /// 创建消息行
    /// - Parameters:
    ///   - avatar: 头像图标名称
    ///   - name: 用户/消息名称
    ///   - lastMessage: 最后一条消息
    ///   - time: 时间
    ///   - unreadCount: 未读数量
    /// - Returns: 消息行视图
    private func createMessageRow(avatar: String, name: String, lastMessage: String, time: String, unreadCount: Int) -> some View {
        Button(action: {
            handleMessageTap(name: name)
        }) {
            HStack(spacing: 12) {
                // 头像
                Image(systemName: avatar)
                    .font(.system(size: 40))
                    .foregroundColor(MHTheme.primaryColor)
                    .frame(width: 50, height: 50)
                
                // 消息内容
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(name)
                            .font(MHTheme.subtitleFont)
                            .foregroundColor(MHTheme.primaryTextColor)
                        
                        Spacer()
                        
                        Text(time)
                            .font(MHTheme.captionFont)
                            .foregroundColor(MHTheme.tertiaryTextColor)
                    }
                    
                    HStack {
                        Text(lastMessage)
                            .font(MHTheme.bodyFont)
                            .foregroundColor(MHTheme.secondaryTextColor)
                            .lineLimit(1)
                        
                        Spacer()
                        
                        // 未读消息徽章
                        if unreadCount > 0 {
                            Text("\(unreadCount)")
                                .font(.system(size: 12, weight: .bold))
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.red)
                                .clipShape(Capsule())
                        }
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
        .buttonStyle(PlainButtonStyle())
        .background(MHTheme.backgroundColor)
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(MHTheme.separatorColor)
                .padding(.leading, 78),
            alignment: .bottom
        )
    }
    
    /// 刷新消息
    /// 异步方法，用于下拉刷新时重新加载数据
    private func refreshMessages() async {
        // TODO: 实现数据刷新逻辑
        // 这里将来会调用API获取最新的消息数据
        
        // 模拟网络请求延迟
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        print("消息刷新完成，当前类型：\(messageTypes[selectedMessageType])")
    }
    
    /// 设置消息视图
    /// 在视图出现时进行必要的初始化操作
    private func setupMessageView() {
        // TODO: 初始化消息数据
        // 这里将来会调用ViewModel的初始化方法
        
        print("MessageView初始化完成，当前类型：\(messageTypes[selectedMessageType])")
    }
    
    /// 处理消息点击事件
    /// - Parameter name: 消息名称
    private func handleMessageTap(name: String) {
        // TODO: 实现消息点击逻辑
        // 这里将来会调用Coordinator导航到聊天页面
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        print("点击了消息: \(name)")
    }
}

// MARK: - 预览
#Preview {
    MessageView()
}
