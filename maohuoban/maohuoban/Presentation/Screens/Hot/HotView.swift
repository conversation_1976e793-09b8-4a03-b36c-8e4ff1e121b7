//
//  HotView.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：热门内容视图
//  重构说明：从HomeView.swift中分离出来，实现Hot模块的独立管理
//  架构模式：MVVM - 这是View层，后续会添加对应的ViewModel
//

import SwiftUI

// MARK: - 热门内容视图
/// 热门内容页面 - 显示平台上的热门动态、话题和用户
/// 包含热门排行榜、趋势话题、推荐内容等功能
/// 支持多种内容类型的展示和筛选
struct HotView: View {
    
    // MARK: - 状态变量
    /// 当前选中的热门类型索引 (0:综合, 1:动态, 2:话题, 3:用户)
    @State private var selectedHotType = 0
    
    /// 是否显示筛选菜单
    @State private var showFilterMenu = false
    
    // MARK: - 常量
    /// 热门类型选项数组
    private let hotTypes = ["综合", "动态", "话题", "用户"]
    
    // MARK: - 视图主体
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // MARK: 顶部筛选栏
                createFilterBar()
                
                // MARK: 内容区域
                ScrollView {
                    LazyVStack(spacing: 16) {
                        // 根据选中的类型显示不同内容
                        switch selectedHotType {
                        case 0:
                            createComprehensiveContent()
                        case 1:
                            createHotPostsContent()
                        case 2:
                            createHotTopicsContent()
                        case 3:
                            createHotUsersContent()
                        default:
                            createComprehensiveContent()
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 16)
                }
                .refreshable {
                    // 下拉刷新
                    await refreshHotContent()
                }
            }
            .navigationTitle("热门")
            .navigationBarTitleDisplayMode(.large)
        }
        .onAppear {
            // 视图出现时的初始化操作
            setupHotView()
        }
    }
    
    // MARK: - 私有方法
    
    /// 创建筛选栏
    /// - Returns: 筛选栏视图
    private func createFilterBar() -> some View {
        HStack {
            // 热门类型选择器
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 20) {
                    ForEach(0..<hotTypes.count, id: \.self) { index in
                        Button(action: {
                            selectedHotType = index
                            
                            // 添加触觉反馈
                            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                            impactFeedback.impactOccurred()
                        }) {
                            VStack(spacing: 4) {
                                Text(hotTypes[index])
                                    .font(.system(size: 16, weight: selectedHotType == index ? .bold : .medium))
                                    .foregroundColor(selectedHotType == index ? MHTheme.primaryColor : MHTheme.secondaryTextColor)
                                
                                // 底部指示器
                                Rectangle()
                                    .frame(height: 2)
                                    .foregroundColor(selectedHotType == index ? MHTheme.primaryColor : .clear)
                            }
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
            
            Spacer()
            
            // 筛选按钮
            Button(action: {
                showFilterMenu.toggle()
            }) {
                Image(systemName: "line.3.horizontal.decrease.circle")
                    .font(.system(size: 20))
                    .foregroundColor(MHTheme.primaryTextColor)
            }
            .padding(.trailing, 16)
        }
        .padding(.vertical, 8)
        .background(MHTheme.backgroundColor)
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(MHTheme.separatorColor),
            alignment: .bottom
        )
    }
    
    /// 创建综合热门内容
    /// - Returns: 综合内容视图
    private func createComprehensiveContent() -> some View {
        VStack(spacing: 16) {
            // TODO: 实现综合热门内容
            ForEach(1...5, id: \.self) { index in
                createPlaceholderCard(title: "综合热门内容 \(index)", subtitle: "这是一个综合热门内容的示例")
            }
        }
    }
    
    /// 创建热门动态内容
    /// - Returns: 热门动态视图
    private func createHotPostsContent() -> some View {
        VStack(spacing: 16) {
            // TODO: 实现热门动态内容
            ForEach(1...5, id: \.self) { index in
                createPlaceholderCard(title: "热门动态 \(index)", subtitle: "这是一个热门动态的示例")
            }
        }
    }
    
    /// 创建热门话题内容
    /// - Returns: 热门话题视图
    private func createHotTopicsContent() -> some View {
        VStack(spacing: 16) {
            // TODO: 实现热门话题内容
            ForEach(1...5, id: \.self) { index in
                createPlaceholderCard(title: "热门话题 \(index)", subtitle: "这是一个热门话题的示例")
            }
        }
    }
    
    /// 创建热门用户内容
    /// - Returns: 热门用户视图
    private func createHotUsersContent() -> some View {
        VStack(spacing: 16) {
            // TODO: 实现热门用户内容
            ForEach(1...5, id: \.self) { index in
                createPlaceholderCard(title: "热门用户 \(index)", subtitle: "这是一个热门用户的示例")
            }
        }
    }
    
    /// 创建占位卡片
    /// - Parameters:
    ///   - title: 标题
    ///   - subtitle: 副标题
    /// - Returns: 占位卡片视图
    private func createPlaceholderCard(title: String, subtitle: String) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(MHTheme.titleFont)
                .foregroundColor(MHTheme.primaryTextColor)
            
            Text(subtitle)
                .font(MHTheme.bodyFont)
                .foregroundColor(MHTheme.secondaryTextColor)
                .lineLimit(2)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(16)
        .background(MHTheme.secondaryBackgroundColor)
        .cornerRadius(MHTheme.cornerRadiusM)
        .onTapGesture {
            handleCardTap(title: title)
        }
    }
    
    /// 刷新热门内容
    /// 异步方法，用于下拉刷新时重新加载数据
    private func refreshHotContent() async {
        // TODO: 实现数据刷新逻辑
        // 这里将来会调用API获取最新的热门数据
        
        // 模拟网络请求延迟
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        print("热门内容刷新完成，当前类型：\(hotTypes[selectedHotType])")
    }
    
    /// 设置热门视图
    /// 在视图出现时进行必要的初始化操作
    private func setupHotView() {
        // TODO: 初始化热门数据
        // 这里将来会调用ViewModel的初始化方法
        
        print("HotView初始化完成，当前类型：\(hotTypes[selectedHotType])")
    }
    
    /// 处理卡片点击事件
    /// - Parameter title: 卡片标题
    private func handleCardTap(title: String) {
        // TODO: 实现卡片点击逻辑
        // 这里将来会调用Coordinator导航到详情页面
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        print("点击了热门卡片: \(title)")
    }
}

// MARK: - 预览
#Preview {
    HotView()
}
