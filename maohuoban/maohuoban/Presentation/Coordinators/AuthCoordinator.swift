import UIKit

/// 认证协调器，管理认证相关的导航流程
class AuthCoordinator: Coordinator {
    var childCoordinators: [Coordinator] = []
    var navigationController: UINavigationController
    
    weak var delegate: AuthCoordinatorDelegate?
    
    /// 初始化认证协调器
    /// - Parameter navigationController: 导航控制器
    init(navigationController: UINavigationController) {
        self.navigationController = navigationController
    }
    
    /// 启动认证协调器
    func start() {
        showLogin()
    }
    
    /// 显示登录界面
    private func showLogin() {
        // 这里我们暂时创建一个占位的视图控制器
        // 在实际实现中，我们会创建真正的登录视图控制器
        let loginVC = UIViewController()
        loginVC.title = "登录"
        loginVC.view.backgroundColor = .white
        
        // 添加一个按钮模拟登录成功
        let loginButton = UIButton(type: .system)
        loginButton.setTitle("登录", for: .normal)
        loginButton.addTarget(self, action: #selector(loginButtonTapped), for: .touchUpInside)
        loginButton.translatesAutoresizingMaskIntoConstraints = false
        
        loginVC.view.addSubview(loginButton)
        NSLayoutConstraint.activate([
            loginButton.centerXAnchor.constraint(equalTo: loginVC.view.centerXAnchor),
            loginButton.centerYAnchor.constraint(equalTo: loginVC.view.centerYAnchor)
        ])
        
        // 添加一个按钮导航到注册界面
        let registerButton = UIButton(type: .system)
        registerButton.setTitle("注册", for: .normal)
        registerButton.addTarget(self, action: #selector(registerButtonTapped), for: .touchUpInside)
        registerButton.translatesAutoresizingMaskIntoConstraints = false
        
        loginVC.view.addSubview(registerButton)
        NSLayoutConstraint.activate([
            registerButton.centerXAnchor.constraint(equalTo: loginVC.view.centerXAnchor),
            registerButton.topAnchor.constraint(equalTo: loginButton.bottomAnchor, constant: 20)
        ])
        
        navigationController.setViewControllers([loginVC], animated: true)
    }
    
    /// 显示注册界面
    private func showRegister() {
        // 这里我们暂时创建一个占位的视图控制器
        // 在实际实现中，我们会创建真正的注册视图控制器
        let registerVC = UIViewController()
        registerVC.title = "注册"
        registerVC.view.backgroundColor = .white
        
        // 添加一个按钮模拟注册成功
        let registerButton = UIButton(type: .system)
        registerButton.setTitle("完成注册", for: .normal)
        registerButton.addTarget(self, action: #selector(registerCompleteButtonTapped), for: .touchUpInside)
        registerButton.translatesAutoresizingMaskIntoConstraints = false
        
        registerVC.view.addSubview(registerButton)
        NSLayoutConstraint.activate([
            registerButton.centerXAnchor.constraint(equalTo: registerVC.view.centerXAnchor),
            registerButton.centerYAnchor.constraint(equalTo: registerVC.view.centerYAnchor)
        ])
        
        navigationController.pushViewController(registerVC, animated: true)
    }
    
    /// 登录按钮点击事件
    @objc private func loginButtonTapped() {
        // 模拟登录成功
        // 在实际实现中，我们会调用登录API
        TokenManager.shared.saveTokens(
            accessToken: "fake_access_token",
            refreshToken: "fake_refresh_token",
            expiresIn: 3600
        )
        
        // 通知代理认证完成
        delegate?.authCoordinatorDidFinish(self)
    }
    
    /// 注册按钮点击事件
    @objc private func registerButtonTapped() {
        showRegister()
    }
    
    /// 完成注册按钮点击事件
    @objc private func registerCompleteButtonTapped() {
        // 模拟注册成功并自动登录
        // 在实际实现中，我们会调用注册API，然后自动登录
        TokenManager.shared.saveTokens(
            accessToken: "fake_access_token",
            refreshToken: "fake_refresh_token",
            expiresIn: 3600
        )
        
        // 通知代理认证完成
        delegate?.authCoordinatorDidFinish(self)
    }
}
