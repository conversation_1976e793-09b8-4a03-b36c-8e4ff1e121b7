import UIKit

/// Coordinator协议，定义了协调器的基本功能
protocol Coordinator: AnyObject {
    /// 子协调器数组
    var childCoordinators: [Coordinator] { get set }
    
    /// 导航控制器
    var navigationController: UINavigationController { get set }
    
    /// 启动协调器
    func start()
    
    /// 结束协调器
    func finish()
}

extension Coordinator {
    /// 添加子协调器
    /// - Parameter coordinator: 要添加的子协调器
    func addChildCoordinator(_ coordinator: Coordinator) {
        childCoordinators.append(coordinator)
    }
    
    /// 移除子协调器
    /// - Parameter coordinator: 要移除的子协调器
    func removeChildCoordinator(_ coordinator: Coordinator) {
        childCoordinators = childCoordinators.filter { $0 !== coordinator }
    }
    
    /// 默认的finish实现
    func finish() {
        childCoordinators.forEach { $0.finish() }
        childCoordinators.removeAll()
    }
}
