import UIKit

/// 应用协调器，作为根协调器管理整个应用的导航流程
class AppCoordinator: Coordinator {
    var childCoordinators: [Coordinator] = []
    var navigationController: UINavigationController
    
    private let window: UIWindow
    
    /// 初始化应用协调器
    /// - Parameter window: 应用窗口
    init(window: UIWindow) {
        self.window = window
        self.navigationController = UINavigationController()
        self.navigationController.navigationBar.prefersLargeTitles = true
    }
    
    /// 启动应用协调器
    func start() {
        // 设置窗口的根视图控制器
        window.rootViewController = navigationController
        window.makeKeyAndVisible()
        
        // 检查用户是否已登录
        if TokenManager.shared.isTokenValid {
            showMainFlow()
        } else {
            showAuthFlow()
        }
    }
    
    /// 显示认证流程
    private func showAuthFlow() {
        let authCoordinator = AuthCoordinator(navigationController: navigationController)
        authCoordinator.delegate = self
        addChildCoordinator(authCoordinator)
        authCoordinator.start()
    }
    
    /// 显示主流程
    private func showMainFlow() {
        let mainCoordinator = MainCoordinator(navigationController: navigationController)
        addChildCoordinator(mainCoordinator)
        mainCoordinator.start()
    }
}

// MARK: - AuthCoordinatorDelegate
extension AppCoordinator: AuthCoordinatorDelegate {
    /// 认证协调器完成回调
    /// - Parameter coordinator: 认证协调器
    func authCoordinatorDidFinish(_ coordinator: AuthCoordinator) {
        removeChildCoordinator(coordinator)
        showMainFlow()
    }
}

/// 认证协调器代理协议
protocol AuthCoordinatorDelegate: AnyObject {
    /// 认证协调器完成回调
    /// - Parameter coordinator: 认证协调器
    func authCoordinatorDidFinish(_ coordinator: AuthCoordinator)
}
