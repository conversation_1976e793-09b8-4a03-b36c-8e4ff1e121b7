import UIKit

/// 主协调器，管理主要功能的导航流程
class MainCoordinator: Coordinator {
    var childCoordinators: [Coordinator] = []
    var navigationController: UINavigationController
    
    /// 初始化主协调器
    /// - Parameter navigationController: 导航控制器
    init(navigationController: UINavigationController) {
        self.navigationController = navigationController
    }
    
    /// 启动主协调器
    func start() {
        showMainTabBar()
    }
    
    /// 显示主标签栏
    private func showMainTabBar() {
        let tabBarController = UITabBarController()
        
        // 创建首页标签
        let homeVC = UIViewController()
        homeVC.title = "首页"
        homeVC.view.backgroundColor = .white
        let homeNav = UINavigationController(rootViewController: homeVC)
        homeNav.tabBarItem = UITabBarItem(title: "首页", image: UIImage(systemName: "house"), tag: 0)
        
        // 创建宠物标签
        let petsVC = UIViewController()
        petsVC.title = "宠物"
        petsVC.view.backgroundColor = .white
        let petsNav = UINavigationController(rootViewController: petsVC)
        petsNav.tabBarItem = UITabBarItem(title: "宠物", image: UIImage(systemName: "pawprint"), tag: 1)
        
        // 创建发布标签
        let publishVC = UIViewController()
        publishVC.title = "发布"
        publishVC.view.backgroundColor = .white
        let publishNav = UINavigationController(rootViewController: publishVC)
        publishNav.tabBarItem = UITabBarItem(title: "发布", image: UIImage(systemName: "plus.circle"), tag: 2)
        
        // 创建消息标签
        let messagesVC = UIViewController()
        messagesVC.title = "消息"
        messagesVC.view.backgroundColor = .white
        let messagesNav = UINavigationController(rootViewController: messagesVC)
        messagesNav.tabBarItem = UITabBarItem(title: "消息", image: UIImage(systemName: "message"), tag: 3)
        
        // 创建我的标签
        let profileVC = UIViewController()
        profileVC.title = "我的"
        profileVC.view.backgroundColor = .white
        
        // 添加退出登录按钮
        let logoutButton = UIButton(type: .system)
        logoutButton.setTitle("退出登录", for: .normal)
        logoutButton.addTarget(self, action: #selector(logoutButtonTapped), for: .touchUpInside)
        logoutButton.translatesAutoresizingMaskIntoConstraints = false
        
        profileVC.view.addSubview(logoutButton)
        NSLayoutConstraint.activate([
            logoutButton.centerXAnchor.constraint(equalTo: profileVC.view.centerXAnchor),
            logoutButton.centerYAnchor.constraint(equalTo: profileVC.view.centerYAnchor)
        ])
        
        let profileNav = UINavigationController(rootViewController: profileVC)
        profileNav.tabBarItem = UITabBarItem(title: "我的", image: UIImage(systemName: "person"), tag: 4)
        
        // 设置标签栏控制器的视图控制器
        tabBarController.viewControllers = [homeNav, petsNav, publishNav, messagesNav, profileNav]
        
        // 设置导航控制器的根视图控制器
        navigationController.setViewControllers([tabBarController], animated: true)
        navigationController.isNavigationBarHidden = true
    }
    
    /// 退出登录按钮点击事件
    @objc private func logoutButtonTapped() {
        // 显示确认对话框
        let alertController = UIAlertController(
            title: "退出登录",
            message: "确定要退出登录吗？",
            preferredStyle: .alert
        )
        
        // 添加取消按钮
        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))
        
        // 添加确认按钮
        alertController.addAction(UIAlertAction(title: "确定", style: .destructive) { [weak self] _ in
            // 清除令牌
            TokenManager.shared.clearTokens()
            
            // 重新显示认证流程
            // 在实际实现中，我们会通知AppCoordinator重新显示认证流程
            // 这里我们简单地重置导航控制器
            self?.navigationController.isNavigationBarHidden = false
            self?.navigationController.popToRootViewController(animated: true)
            
            // 通知AppCoordinator
            NotificationCenter.default.post(name: NSNotification.Name("LogoutNotification"), object: nil)
        })
        
        // 显示对话框
        navigationController.present(alertController, animated: true)
    }
}
