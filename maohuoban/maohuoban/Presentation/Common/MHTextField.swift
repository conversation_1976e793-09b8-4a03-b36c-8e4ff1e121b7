import UIKit

/// 自定义文本输入框类
class MHTextField: UITextField {
    
    /// 左侧内边距
    private let leftPadding: CGFloat = 16
    
    /// 右侧内边距
    private let rightPadding: CGFloat = 16
    
    /// 占位符颜色
    private let placeholderColor = UIColor.gray.withAlphaComponent(0.5)
    
    /// 初始化方法
    /// - Parameter placeholder: 占位符文本
    init(placeholder: String) {
        super.init(frame: .zero)
        self.placeholder = placeholder
        self.setupTextField()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        self.setupTextField()
    }
    
    /// 设置文本输入框
    private func setupTextField() {
        self.backgroundColor = UIColor(red: 0.95, green: 0.95, blue: 0.95, alpha: 1.0)
        self.layer.cornerRadius = 8
        self.font = UIFont.systemFont(ofSize: 16)
        self.textColor = UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0)
        
        // 设置占位符颜色
        if let placeholder = self.placeholder {
            self.attributedPlaceholder = NSAttributedString(
                string: placeholder,
                attributes: [NSAttributedString.Key.foregroundColor: placeholderColor]
            )
        }
        
        // 添加底部边框
        let bottomBorder = CALayer()
        bottomBorder.frame = CGRect(x: 0, y: self.frame.height - 1, width: self.frame.width, height: 1)
        bottomBorder.backgroundColor = UIColor.lightGray.cgColor
        self.layer.addSublayer(bottomBorder)
        
        // 设置内边距
        self.leftView = UIView(frame: CGRect(x: 0, y: 0, width: leftPadding, height: self.frame.height))
        self.leftViewMode = .always
        self.rightView = UIView(frame: CGRect(x: 0, y: 0, width: rightPadding, height: self.frame.height))
        self.rightViewMode = .always
    }
    
    /// 布局子视图
    override func layoutSubviews() {
        super.layoutSubviews()
        
        // 更新底部边框的宽度
        if let bottomBorder = self.layer.sublayers?.first {
            bottomBorder.frame = CGRect(x: 0, y: self.frame.height - 1, width: self.frame.width, height: 1)
        }
    }
    
    /// 设置文本矩形区域
    override func textRect(forBounds bounds: CGRect) -> CGRect {
        return bounds.inset(by: UIEdgeInsets(top: 0, left: leftPadding, bottom: 0, right: rightPadding))
    }
    
    /// 设置编辑时的文本矩形区域
    override func editingRect(forBounds bounds: CGRect) -> CGRect {
        return bounds.inset(by: UIEdgeInsets(top: 0, left: leftPadding, bottom: 0, right: rightPadding))
    }
    
    /// 设置占位符矩形区域
    override func placeholderRect(forBounds bounds: CGRect) -> CGRect {
        return bounds.inset(by: UIEdgeInsets(top: 0, left: leftPadding, bottom: 0, right: rightPadding))
    }
}
