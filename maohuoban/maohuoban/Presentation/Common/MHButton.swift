import UIKit

/// 按钮样式枚举
enum MHButtonStyle {
    case primary
    case secondary
    case outline
    case danger
}

/// 自定义按钮类
class MHButton: UIButton {

    /// 按钮样式
    private var style: MHButtonStyle = .primary

    /// 初始化方法
    /// - Parameters:
    ///   - style: 按钮样式
    ///   - title: 按钮标题
    init(style: MHButtonStyle, title: String) {
        // 使用指定的初始化方法
        super.init(frame: .zero)

        // 设置属性
        self.style = style
        self.setTitle(title, for: .normal)

        // 设置按钮样式
        self.setupButton()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        self.setupButton()
    }

    /// 设置按钮样式
    /// - Parameter style: 按钮样式
    func setStyle(_ style: MHButtonStyle) {
        self.style = style
        self.setupButton()
    }

    /// 设置按钮
    private func setupButton() {
        if #available(iOS 15.0, *) {
            // 使用 UIButtonConfiguration
            var config: UIButton.Configuration

            switch style {
            case .primary:
                config = .filled()
                config.baseBackgroundColor = UIColor(red: 1.0, green: 0.35, blue: 0.37, alpha: 1.0) // #FF5A5F
                config.baseForegroundColor = .white

            case .secondary:
                config = .filled()
                config.baseBackgroundColor = UIColor(red: 0.0, green: 0.65, blue: 0.6, alpha: 1.0) // #00A699
                config.baseForegroundColor = .white

            case .outline:
                config = .bordered()
                config.baseBackgroundColor = .clear
                config.baseForegroundColor = UIColor(red: 1.0, green: 0.35, blue: 0.37, alpha: 1.0)
                config.background.strokeColor = UIColor(red: 1.0, green: 0.35, blue: 0.37, alpha: 1.0)
                config.background.strokeWidth = 1.0

            case .danger:
                config = .filled()
                config.baseBackgroundColor = UIColor(red: 0.9, green: 0.2, blue: 0.2, alpha: 1.0) // #E63333
                config.baseForegroundColor = .white
            }

            config.cornerStyle = .medium
            config.contentInsets = NSDirectionalEdgeInsets(top: 12, leading: 20, bottom: 12, trailing: 20)

            // 设置字体
            var titleAttr = AttributedString(self.title(for: .normal) ?? "")
            titleAttr.font = .systemFont(ofSize: 16, weight: .medium)
            config.attributedTitle = titleAttr

            self.configuration = config

        } else {
            // 兼容 iOS 15 以下版本
            self.layer.cornerRadius = 8
            self.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)

            // 使用 contentEdgeInsets 设置内边距
            // 注意：这个属性在 iOS 15 中已弃用，但在旧版本中仍然需要使用
            #if compiler(>=5.5) && canImport(UIKit)
                if #unavailable(iOS 15.0) {
                    self.contentEdgeInsets = UIEdgeInsets(top: 12, left: 20, bottom: 12, right: 20)
                }
            #else
                self.contentEdgeInsets = UIEdgeInsets(top: 12, left: 20, bottom: 12, right: 20)
            #endif

            switch style {
            case .primary:
                self.backgroundColor = UIColor(red: 1.0, green: 0.35, blue: 0.37, alpha: 1.0) // #FF5A5F
                self.setTitleColor(.white, for: .normal)
                self.layer.borderWidth = 0
            case .secondary:
                self.backgroundColor = UIColor(red: 0.0, green: 0.65, blue: 0.6, alpha: 1.0) // #00A699
                self.setTitleColor(.white, for: .normal)
                self.layer.borderWidth = 0
            case .outline:
                self.backgroundColor = .clear
                self.setTitleColor(UIColor(red: 1.0, green: 0.35, blue: 0.37, alpha: 1.0), for: .normal)
                self.layer.borderWidth = 1
                self.layer.borderColor = UIColor(red: 1.0, green: 0.35, blue: 0.37, alpha: 1.0).cgColor
            case .danger:
                self.backgroundColor = UIColor(red: 0.9, green: 0.2, blue: 0.2, alpha: 1.0) // #E63333
                self.setTitleColor(.white, for: .normal)
                self.layer.borderWidth = 0
            }
        }
    }

    /// 按钮点击时的动画
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesBegan(touches, with: event)
        UIView.animate(withDuration: 0.1) {
            self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }
    }

    /// 按钮释放时的动画
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesEnded(touches, with: event)
        UIView.animate(withDuration: 0.1) {
            self.transform = CGAffineTransform.identity
        }
    }

    /// 按钮取消时的动画
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        super.touchesCancelled(touches, with: event)
        UIView.animate(withDuration: 0.1) {
            self.transform = CGAffineTransform.identity
        }
    }
}
