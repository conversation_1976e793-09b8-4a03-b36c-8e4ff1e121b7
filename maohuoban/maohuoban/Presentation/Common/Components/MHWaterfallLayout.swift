//
//  MHWaterfallLayout.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：瀑布流布局组件
//  重构说明：从HomeView.swift中分离出来，实现组件的模块化和可重用性
//  功能：实现双列瀑布流布局，展示Feed内容卡片
//

import SwiftUI

// MARK: - 瀑布流布局组件
/// 毛伙伴应用瀑布流布局组件
/// 实现双列不等高的卡片布局，适用于Feed流、图片展示等场景
/// 支持动态内容高度和响应式布局
struct MHWaterfallLayout: View {
    
    // MARK: - 示例数据
    // TODO: 后续将通过ViewModel提供真实数据
    /// 左列数据项
    private let leftItems = [
        FeedItem(
            id: 1, 
            title: "奔跑吧已经13季就一季来过上海，之后怎么再也不来了吗？是因为费用太高了吗？还是就他们之后再也不来了~", 
            author: "奶茶爱冰冰", 
            likes: 34, 
            hasImage: false
        ),
        FeedItem(
            id: 3, 
            title: "手动挡直接踩刹车停车会熄火吗？", 
            author: "", 
            likes: 0, 
            hasImage: true
        )
    ]
    
    /// 右列数据项
    private let rightItems = [
        FeedItem(
            id: 2, 
            title: "奇瑞这波操作真的666", 
            author: "汽车信息", 
            likes: 73, 
            hasImage: true
        ),
        FeedItem(
            id: 4, 
            title: "自建了一个小型光伏电站，3块板日发电4度", 
            author: "Zeruns", 
            likes: 66, 
            hasImage: true
        ),
        FeedItem(
            id: 5, 
            title: "Henry的Apple Watch", 
            author: "", 
            likes: 0, 
            hasImage: true
        )
    ]
    
    // MARK: - 布局常量
    /// 列间距
    private let columnSpacing: CGFloat = 10
    
    /// 行间距
    private let rowSpacing: CGFloat = 10
    
    /// 水平内边距
    private let horizontalPadding: CGFloat = 5
    
    /// 顶部内边距
    private let topPadding: CGFloat = 10
    
    /// 单列宽度比例（相对于屏幕宽度）
    private let columnWidthRatio: CGFloat = 0.48
    
    // MARK: - 计算属性
    /// 屏幕宽度
    private var screenWidth: CGFloat {
        UIScreen.main.bounds.width
    }
    
    /// 单列宽度
    private var columnWidth: CGFloat {
        screenWidth * columnWidthRatio
    }
    
    // MARK: - 视图主体
    var body: some View {
        HStack(alignment: .top, spacing: columnSpacing) {
            // MARK: 左列
            VStack(spacing: rowSpacing) {
                ForEach(leftItems) { item in
                    MHFeedCard(item: item)
                        .onTapGesture {
                            handleCardTap(item)
                        }
                }
            }
            .frame(width: columnWidth)
            
            // MARK: 右列
            VStack(spacing: rowSpacing) {
                ForEach(rightItems) { item in
                    MHFeedCard(item: item)
                        .onTapGesture {
                            handleCardTap(item)
                        }
                }
            }
            .frame(width: columnWidth)
        }
        .padding(.horizontal, horizontalPadding)
        .padding(.top, topPadding)
    }
    
    // MARK: - 私有方法
    
    /// 处理卡片点击事件
    /// - Parameter item: 被点击的Feed项
    private func handleCardTap(_ item: FeedItem) {
        // TODO: 实现卡片点击逻辑
        // 这里将来会调用Coordinator导航到详情页面
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        print("点击了Feed卡片: \(item.title)")
    }
}

// MARK: - Feed数据模型
/// Feed项数据模型
/// 用于表示单个动态内容的数据结构
struct FeedItem: Identifiable {
    /// 唯一标识符
    let id: Int
    
    /// 内容标题/文本
    let title: String
    
    /// 作者名称
    let author: String
    
    /// 点赞数
    let likes: Int
    
    /// 是否包含图片
    let hasImage: Bool
}

// MARK: - 预览
#Preview {
    ScrollView {
        MHWaterfallLayout()
    }
    .background(Color.gray.opacity(0.1))
}
