//
//  MHTopNavigationBar.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：自定义顶部导航栏组件
//  重构说明：从HomeView.swift中分离出来，实现组件的模块化和可重用性
//  功能：包含菜单按钮、标签切换、搜索按钮等功能
//

import SwiftUI

// MARK: - 自定义顶部导航栏组件
/// 毛伙伴应用自定义顶部导航栏
/// 包含左侧菜单按钮、中间标签切换、右侧搜索按钮
/// 支持动态标签数量和自定义样式
struct MHTopNavigationBar: View {

    // MARK: - 绑定属性
    /// 搜索文本绑定
    @Binding var searchText: String

    /// 当前选中的主标签索引
    @Binding var selectedMainTab: Int

    // MARK: - 配置属性
    /// 主标签数组
    let mainTabs: [String]

    /// 是否显示位置图标
    let showLocationIcon: Bool

    // MARK: - 常量
    /// 标签间距 - 调整为更紧凑的间距，保持与原始设计一致
    private let tabSpacing: CGFloat = 12

    /// 导航栏水平内边距
    private let horizontalPadding: CGFloat = 15

    /// 导航栏垂直内边距
    private let verticalPadding: CGFloat = 10

    // MARK: - 视图主体
    var body: some View {
        ZStack {
            // MARK: 左右两侧按钮容器
            HStack {
                // 左侧菜单按钮
                createMenuButton()

                Spacer()

                // 右侧搜索按钮
                createSearchButton()
            }

            // MARK: 中间标签切换器 - 居中显示
            HStack(spacing: 0) { // 测试：设置为0间距，应该非常紧密
                ForEach(0..<mainTabs.count, id: \.self) { index in
                    createTabButton(for: index)
                }
            }
        }
        .padding(.horizontal, horizontalPadding)
        .padding(.top, verticalPadding)
        .padding(.bottom, 5)
        .background(MHTheme.backgroundColor) // 使用主题背景色
    }

    // MARK: - 私有方法

    /// 创建菜单按钮
    /// - Returns: 菜单按钮视图
    private func createMenuButton() -> some View {
        Button(action: {
            // TODO: 打开侧边菜单
            // 这里将来会调用Coordinator打开菜单页面
            handleMenuButtonTap()
        }) {
            VStack(spacing: 5) { // 三条线之间的间距
                ForEach(0..<3) { _ in
                    Rectangle()
                        .fill(MHTheme.primaryTextColor) // 使用主题文本颜色
                        .frame(width: 22, height: 2)
                        .cornerRadius(1) // 轻微圆角
                }
            }
            .frame(width: 22, height: 24) // 整体尺寸
            .padding(4) // 增加点击区域
        }
        .buttonStyle(PlainButtonStyle()) // 移除默认按钮样式
    }

    /// 创建搜索按钮
    /// - Returns: 搜索按钮视图
    private func createSearchButton() -> some View {
        Button(action: {
            // TODO: 打开搜索界面
            // 这里将来会调用Coordinator打开搜索页面
            handleSearchButtonTap()
        }) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 20))
                .foregroundColor(MHTheme.primaryTextColor) // 使用主题文本颜色
        }
        .buttonStyle(PlainButtonStyle()) // 移除默认按钮样式
    }

    /// 创建标签按钮
    /// - Parameter index: 标签索引
    /// - Returns: 标签按钮视图
    private func createTabButton(for index: Int) -> some View {
        Button(action: {
            // 切换标签
            selectedMainTab = index

            // 添加触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()

            // 日志记录
            print("切换到标签: \(mainTabs[index])")
        }) {
            VStack(spacing: 0) {
                Text(mainTabs[index])
                    .font(.system(
                        size: 16,
                        weight: selectedMainTab == index ? .bold : .regular
                    )) // 选中时字体加粗
                    .foregroundColor(
                        selectedMainTab == index
                        ? MHTheme.primaryTextColor
                        : MHTheme.secondaryTextColor
                    ) // 选中时使用主要文本颜色

                // 底部指示器
                Rectangle()
                    .frame(height: 2)
                    .foregroundColor(
                        selectedMainTab == index
                        ? MHTheme.primaryColor
                        : .clear
                    ) // 选中时显示主题色指示器
                    .offset(y: 2) // 调整指示器位置
            }
        }
        .buttonStyle(PlainButtonStyle()) // 移除默认按钮样式
    }

    /// 处理菜单按钮点击
    private func handleMenuButtonTap() {
        // TODO: 实现菜单功能
        // 这里将来会调用Coordinator显示侧边菜单
        print("点击菜单按钮")
    }

    /// 处理搜索按钮点击
    private func handleSearchButtonTap() {
        // TODO: 实现搜索功能
        // 这里将来会调用Coordinator显示搜索页面
        print("点击搜索按钮")
    }
}

// MARK: - 预览
#Preview {
    VStack {
        MHTopNavigationBar(
            searchText: .constant(""),
            selectedMainTab: .constant(1),
            mainTabs: ["关注", "发现", "附近"],
            showLocationIcon: false
        )

        Spacer()
    }
    .background(Color.gray.opacity(0.1))
}
