//
//  MHFeedCard.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：Feed内容卡片组件
//  重构说明：从HomeView.swift中分离出来，实现组件的模块化和可重用性
//  功能：展示单个Feed项的内容，包括文本、图片、用户信息和互动数据
//

import SwiftUI

// MARK: - Feed内容卡片组件
/// 毛伙伴应用Feed内容卡片
/// 用于展示单个动态内容，支持纯文本、图片、视频等多种内容类型
/// 包含用户信息、点赞数等互动元素
struct MHFeedCard: View {
    
    // MARK: - 属性
    /// Feed项数据
    let item: FeedItem
    
    // MARK: - 布局常量
    /// 卡片内边距
    private let cardPadding: CGFloat = 10
    
    /// 元素间距
    private let elementSpacing: CGFloat = 8
    
    /// 用户信息区域间距
    private let userInfoSpacing: CGFloat = 5
    
    /// 互动区域间距
    private let interactionSpacing: CGFloat = 3
    
    /// 用户头像尺寸
    private let avatarSize: CGFloat = 20
    
    /// 卡片圆角
    private let cardCornerRadius: CGFloat = 8
    
    // MARK: - 视图主体
    var body: some View {
        VStack(alignment: .leading, spacing: elementSpacing) {
            // MARK: 内容文字区域
            if shouldShowText {
                createTextContent()
            }
            
            // MARK: 内容图片区域
            if item.hasImage {
                createImageContent()
            }
            
            // MARK: 用户信息和互动区域
            if shouldShowUserInfo {
                createUserInfoSection()
            }
        }
        .background(MHTheme.backgroundColor) // 使用主题背景色
        .cornerRadius(cardCornerRadius)
        .themeShadow("light") // 应用轻微阴影
    }
    
    // MARK: - 计算属性
    
    /// 是否应该显示文字内容
    private var shouldShowText: Bool {
        !item.hasImage || (item.hasImage && !item.title.isEmpty)
    }
    
    /// 是否应该显示用户信息
    private var shouldShowUserInfo: Bool {
        !item.author.isEmpty
    }
    
    /// 文字行数限制
    private var textLineLimit: Int {
        item.hasImage ? 2 : 6
    }
    
    // MARK: - 私有方法
    
    /// 创建文字内容
    /// - Returns: 文字内容视图
    private func createTextContent() -> some View {
        Text(item.title)
            .font(MHTheme.captionFont) // 使用主题字体
            .foregroundColor(MHTheme.primaryTextColor) // 使用主题文本颜色
            .lineLimit(textLineLimit)
            .multilineTextAlignment(.leading)
            .padding(.horizontal, cardPadding)
            .padding(.top, cardPadding)
    }
    
    /// 创建图片内容
    /// - Returns: 图片内容视图
    private func createImageContent() -> some View {
        ZStack {
            // 占位背景
            Rectangle()
                .fill(MHTheme.lightGrayColor) // 使用主题浅灰色
                .aspectRatio(1.0, contentMode: .fit)
            
            // 视频播放按钮（特定条件下显示）
            if isVideoContent {
                createVideoPlayButton()
            }
            
            // TODO: 这里将来会加载真实图片
            // AsyncImage或其他图片加载组件
        }
        .clipped() // 裁剪超出边界的内容
    }
    
    /// 创建视频播放按钮
    /// - Returns: 播放按钮视图
    private func createVideoPlayButton() -> some View {
        Button(action: {
            handleVideoPlay()
        }) {
            Image(systemName: "play.circle")
                .font(.system(size: 30))
                .foregroundColor(.white)
                .background(
                    Circle()
                        .fill(Color.black.opacity(0.3))
                        .frame(width: 50, height: 50)
                )
        }
    }
    
    /// 创建用户信息区域
    /// - Returns: 用户信息视图
    private func createUserInfoSection() -> some View {
        HStack {
            // 用户头像和名称
            createUserInfo()
            
            Spacer()
            
            // 点赞信息
            createLikeInfo()
        }
        .padding(.horizontal, cardPadding)
        .padding(.bottom, cardPadding)
    }
    
    /// 创建用户信息
    /// - Returns: 用户信息视图
    private func createUserInfo() -> some View {
        HStack(spacing: userInfoSpacing) {
            // 用户头像
            Button(action: {
                handleUserAvatarTap()
            }) {
                Circle()
                    .fill(MHTheme.mediumGrayColor) // 使用主题中等灰色
                    .frame(width: avatarSize, height: avatarSize)
                    .overlay(
                        // TODO: 这里将来会显示真实头像
                        Image(systemName: "person.fill")
                            .font(.system(size: 10))
                            .foregroundColor(MHTheme.secondaryTextColor)
                    )
            }
            
            // 用户名称
            Button(action: {
                handleUserNameTap()
            }) {
                Text(item.author)
                    .font(MHTheme.footnoteFont) // 使用主题小号字体
                    .foregroundColor(MHTheme.secondaryTextColor) // 使用主题次要文本颜色
            }
        }
    }
    
    /// 创建点赞信息
    /// - Returns: 点赞信息视图
    private func createLikeInfo() -> some View {
        Button(action: {
            handleLikeTap()
        }) {
            HStack(spacing: interactionSpacing) {
                Image(systemName: "heart")
                    .font(.system(size: 12))
                
                Text("\(item.likes)")
                    .font(MHTheme.footnoteFont) // 使用主题小号字体
            }
            .foregroundColor(MHTheme.secondaryTextColor) // 使用主题次要文本颜色
        }
    }
    
    // MARK: - 计算属性（内容判断）
    
    /// 是否为视频内容
    private var isVideoContent: Bool {
        // 这里使用简单的ID判断，实际应该根据内容类型字段判断
        item.id == 5
    }
    
    // MARK: - 事件处理方法
    
    /// 处理视频播放
    private func handleVideoPlay() {
        // TODO: 实现视频播放功能
        print("播放视频: \(item.title)")
    }
    
    /// 处理用户头像点击
    private func handleUserAvatarTap() {
        // TODO: 导航到用户资料页面
        print("点击用户头像: \(item.author)")
    }
    
    /// 处理用户名称点击
    private func handleUserNameTap() {
        // TODO: 导航到用户资料页面
        print("点击用户名称: \(item.author)")
    }
    
    /// 处理点赞点击
    private func handleLikeTap() {
        // TODO: 实现点赞功能
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        print("点赞Feed: \(item.title)")
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 10) {
        // 纯文本卡片
        MHFeedCard(item: FeedItem(
            id: 1,
            title: "这是一个纯文本的动态内容，用于测试卡片的文字显示效果。",
            author: "测试用户",
            likes: 42,
            hasImage: false
        ))
        
        // 图片卡片
        MHFeedCard(item: FeedItem(
            id: 2,
            title: "这是一个包含图片的动态",
            author: "图片用户",
            likes: 128,
            hasImage: true
        ))
        
        // 视频卡片
        MHFeedCard(item: FeedItem(
            id: 5,
            title: "这是一个视频内容",
            author: "视频用户",
            likes: 256,
            hasImage: true
        ))
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
