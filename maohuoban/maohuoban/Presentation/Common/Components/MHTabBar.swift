//
//  MHTabBar.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：自定义底部导航栏组件
//  重构说明：从HomeView.swift中分离出来，实现组件的模块化和可重用性
//

import SwiftUI

// MARK: - 自定义底部导航栏
/// 毛伙伴应用自定义底部导航栏
/// 包含5个导航项：首页、社区、发布、消息、我的
/// 中间的发布按钮采用特殊的椭圆形设计
struct MHTabBar: View {
    
    // MARK: - 绑定属性
    /// 当前选中的标签页索引
    @Binding var selectedTab: Int
    
    // MARK: - 常量
    /// 底部导航项标题数组
    /// 注意：索引2为空字符串，对应中间的发布按钮
    private let tabItems = ["首页", "社区", "", "消息", "我"]
    
    /// 屏幕宽度
    private let screenWidth = UIScreen.main.bounds.width
    
    /// 单个标签项宽度
    private var tabItemWidth: CGFloat {
        screenWidth / 5
    }
    
    // MARK: - 视图主体
    var body: some View {
        HStack(spacing: 0) {
            // 遍历创建5个导航项
            ForEach(0..<5) { index in
                if index == 2 {
                    // MARK: 中间发布按钮 - 特殊样式
                    createPublishButton()
                } else {
                    // MARK: 普通导航按钮
                    createTabButton(for: index)
                }
            }
        }
        .background(MHTheme.backgroundColor) // 使用主题背景色
        .cornerRadius(15, corners: [.topLeft, .topRight]) // 顶部圆角
        .themeShadow("light") // 应用主题阴影
    }
    
    // MARK: - 私有方法
    
    /// 创建发布按钮
    /// - Returns: 发布按钮视图
    private func createPublishButton() -> some View {
        Button(action: {
            // TODO: 显示发布界面
            // 这里将来会调用发布功能的Coordinator
            print("点击发布按钮")
        }) {
            ZStack {
                // 椭圆形背景 - 使用主题色
                RoundedRectangle(cornerRadius: 10)
                    .foregroundColor(MHTheme.primaryColor)
                    .frame(width: 65, height: 42)
                
                // 白色加号图标
                Image(systemName: "plus")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
            }
        }
        .offset(y: 1) // 轻微向下偏移，使按钮更贴近底部
        .frame(width: tabItemWidth) // 保持与其他按钮相同的宽度
    }
    
    /// 创建普通标签按钮
    /// - Parameter index: 按钮索引
    /// - Returns: 标签按钮视图
    private func createTabButton(for index: Int) -> some View {
        Button(action: {
            // 切换到对应的标签页
            selectedTab = index
            
            // 添加触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
            
            // 日志记录
            print("切换到标签页: \(tabItems[index])")
        }) {
            Text(tabItems[index])
                .font(.system(
                    size: 17,
                    weight: selectedTab == index ? .bold : .regular
                )) // 选中时字体加粗
                .foregroundColor(
                    selectedTab == index 
                    ? MHTheme.primaryTextColor 
                    : MHTheme.secondaryTextColor
                ) // 选中时使用主要文本颜色，未选中时使用次要文本颜色
                .frame(width: tabItemWidth, height: 50) // 固定按钮尺寸
        }
        .buttonStyle(PlainButtonStyle()) // 移除默认按钮样式
    }
}

// MARK: - 扩展：圆角支持
extension View {
    /// 为视图添加指定角的圆角
    /// - Parameters:
    ///   - radius: 圆角半径
    ///   - corners: 需要圆角的角
    /// - Returns: 应用圆角的视图
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

/// 自定义圆角形状
struct RoundedCorner: Shape {
    /// 圆角半径
    var radius: CGFloat = .infinity
    
    /// 需要圆角的角
    var corners: UIRectCorner = .allCorners
    
    /// 创建路径
    /// - Parameter rect: 绘制区域
    /// - Returns: 路径
    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// MARK: - 预览
#Preview {
    VStack {
        Spacer()
        MHTabBar(selectedTab: .constant(0))
    }
    .background(Color.gray.opacity(0.1))
}
