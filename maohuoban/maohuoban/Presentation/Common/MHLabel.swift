import UIKit

/// 标签样式枚举
enum MHLabelStyle {
    case title
    case subtitle
    case body
    case caption
}

/// 自定义标签类
class MHLabel: UILabel {
    
    /// 标签样式
    private var style: MHLabelStyle = .body
    
    /// 初始化方法
    /// - Parameters:
    ///   - style: 标签样式
    ///   - text: 标签文本
    init(style: MHLabelStyle, text: String) {
        super.init(frame: .zero)
        self.style = style
        self.text = text
        self.setupLabel()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        self.setupLabel()
    }
    
    /// 设置标签样式
    /// - Parameter style: 标签样式
    func setStyle(_ style: MHLabelStyle) {
        self.style = style
        self.setupLabel()
    }
    
    /// 设置标签
    private func setupLabel() {
        switch style {
        case .title:
            self.font = UIFont.systemFont(ofSize: 24, weight: .bold)
            self.textColor = UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0)
        case .subtitle:
            self.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
            self.textColor = UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0)
        case .body:
            self.font = UIFont.systemFont(ofSize: 16, weight: .regular)
            self.textColor = UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0)
        case .caption:
            self.font = UIFont.systemFont(ofSize: 14, weight: .regular)
            self.textColor = UIColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 1.0)
        }
    }
}
