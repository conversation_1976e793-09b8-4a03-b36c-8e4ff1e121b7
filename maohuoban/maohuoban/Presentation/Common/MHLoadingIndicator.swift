import UIKit

/// 自定义加载指示器类
class MHLoadingIndicator: UIView {
    
    /// 活动指示器
    private let activityIndicator = UIActivityIndicatorView(style: .large)
    
    /// 标签
    private let label = UILabel()
    
    /// 初始化方法
    /// - Parameter message: 加载消息
    init(message: String = "加载中...") {
        super.init(frame: .zero)
        self.setupView(message: message)
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        self.setupView(message: "加载中...")
    }
    
    /// 设置视图
    /// - Parameter message: 加载消息
    private func setupView(message: String) {
        // 设置背景
        self.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        self.layer.cornerRadius = 10
        
        // 设置活动指示器
        activityIndicator.color = .white
        activityIndicator.translatesAutoresizingMaskIntoConstraints = false
        self.addSubview(activityIndicator)
        
        // 设置标签
        label.text = message
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 16)
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        self.addSubview(label)
        
        // 设置约束
        NSLayoutConstraint.activate([
            activityIndicator.centerXAnchor.constraint(equalTo: self.centerXAnchor),
            activityIndicator.topAnchor.constraint(equalTo: self.topAnchor, constant: 20),
            
            label.centerXAnchor.constraint(equalTo: self.centerXAnchor),
            label.topAnchor.constraint(equalTo: activityIndicator.bottomAnchor, constant: 10),
            label.leadingAnchor.constraint(equalTo: self.leadingAnchor, constant: 20),
            label.trailingAnchor.constraint(equalTo: self.trailingAnchor, constant: -20),
            label.bottomAnchor.constraint(equalTo: self.bottomAnchor, constant: -20)
        ])
    }
    
    /// 开始加载动画
    func startAnimating() {
        activityIndicator.startAnimating()
    }
    
    /// 停止加载动画
    func stopAnimating() {
        activityIndicator.stopAnimating()
    }
    
    /// 更新加载消息
    /// - Parameter message: 新的加载消息
    func updateMessage(_ message: String) {
        label.text = message
    }
    
    /// 显示在视图控制器上
    /// - Parameters:
    ///   - viewController: 视图控制器
    ///   - animated: 是否使用动画
    func show(in viewController: UIViewController, animated: Bool = true) {
        self.translatesAutoresizingMaskIntoConstraints = false
        viewController.view.addSubview(self)
        
        NSLayoutConstraint.activate([
            self.centerXAnchor.constraint(equalTo: viewController.view.centerXAnchor),
            self.centerYAnchor.constraint(equalTo: viewController.view.centerYAnchor),
            self.widthAnchor.constraint(equalToConstant: 200),
            self.heightAnchor.constraint(greaterThanOrEqualToConstant: 100)
        ])
        
        if animated {
            self.alpha = 0
            UIView.animate(withDuration: 0.3) {
                self.alpha = 1
            }
        }
        
        startAnimating()
    }
    
    /// 从父视图中隐藏
    /// - Parameter animated: 是否使用动画
    func hide(animated: Bool = true) {
        if animated {
            UIView.animate(withDuration: 0.3, animations: {
                self.alpha = 0
            }) { _ in
                self.stopAnimating()
                self.removeFromSuperview()
            }
        } else {
            self.stopAnimating()
            self.removeFromSuperview()
        }
    }
}
