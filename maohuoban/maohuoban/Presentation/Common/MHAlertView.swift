import UIKit

/// 自定义警告视图类
class MHAlertView: UIView {
    
    /// 标题标签
    private let titleLabel = UILabel()
    
    /// 消息标签
    private let messageLabel = UILabel()
    
    /// 确认按钮
    private let confirmButton = MHButton(style: .primary, title: "确定")
    
    /// 取消按钮
    private let cancelButton = MHButton(style: .outline, title: "取消")
    
    /// 确认回调
    private var confirmAction: (() -> Void)?
    
    /// 取消回调
    private var cancelAction: (() -> Void)?
    
    /// 初始化方法
    /// - Parameters:
    ///   - title: 标题
    ///   - message: 消息
    ///   - confirmButtonTitle: 确认按钮标题
    ///   - cancelButtonTitle: 取消按钮标题
    ///   - showCancelButton: 是否显示取消按钮
    init(title: String, message: String, confirmButtonTitle: String = "确定", cancelButtonTitle: String = "取消", showCancelButton: Bool = true) {
        super.init(frame: .zero)
        self.setupView(title: title, message: message, confirmButtonTitle: confirmButtonTitle, cancelButtonTitle: cancelButtonTitle, showCancelButton: showCancelButton)
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        self.setupView(title: "", message: "", confirmButtonTitle: "确定", cancelButtonTitle: "取消", showCancelButton: true)
    }
    
    /// 设置视图
    /// - Parameters:
    ///   - title: 标题
    ///   - message: 消息
    ///   - confirmButtonTitle: 确认按钮标题
    ///   - cancelButtonTitle: 取消按钮标题
    ///   - showCancelButton: 是否显示取消按钮
    private func setupView(title: String, message: String, confirmButtonTitle: String, cancelButtonTitle: String, showCancelButton: Bool) {
        // 设置背景
        self.backgroundColor = .white
        self.layer.cornerRadius = 12
        self.layer.shadowColor = UIColor.black.cgColor
        self.layer.shadowOffset = CGSize(width: 0, height: 2)
        self.layer.shadowRadius = 6
        self.layer.shadowOpacity = 0.1
        
        // 设置标题标签
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = UIColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0)
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        self.addSubview(titleLabel)
        
        // 设置消息标签
        messageLabel.text = message
        messageLabel.font = UIFont.systemFont(ofSize: 16)
        messageLabel.textColor = UIColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 1.0)
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        self.addSubview(messageLabel)
        
        // 设置确认按钮
        confirmButton.setTitle(confirmButtonTitle, for: .normal)
        confirmButton.addTarget(self, action: #selector(confirmButtonTapped), for: .touchUpInside)
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        self.addSubview(confirmButton)
        
        // 设置约束
        if showCancelButton {
            // 设置取消按钮
            cancelButton.setTitle(cancelButtonTitle, for: .normal)
            cancelButton.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
            cancelButton.translatesAutoresizingMaskIntoConstraints = false
            self.addSubview(cancelButton)
            
            // 设置水平按钮布局
            NSLayoutConstraint.activate([
                titleLabel.topAnchor.constraint(equalTo: self.topAnchor, constant: 20),
                titleLabel.leadingAnchor.constraint(equalTo: self.leadingAnchor, constant: 20),
                titleLabel.trailingAnchor.constraint(equalTo: self.trailingAnchor, constant: -20),
                
                messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 10),
                messageLabel.leadingAnchor.constraint(equalTo: self.leadingAnchor, constant: 20),
                messageLabel.trailingAnchor.constraint(equalTo: self.trailingAnchor, constant: -20),
                
                cancelButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 20),
                cancelButton.leadingAnchor.constraint(equalTo: self.leadingAnchor, constant: 20),
                cancelButton.bottomAnchor.constraint(equalTo: self.bottomAnchor, constant: -20),
                cancelButton.widthAnchor.constraint(equalTo: confirmButton.widthAnchor),
                
                confirmButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 20),
                confirmButton.leadingAnchor.constraint(equalTo: cancelButton.trailingAnchor, constant: 10),
                confirmButton.trailingAnchor.constraint(equalTo: self.trailingAnchor, constant: -20),
                confirmButton.bottomAnchor.constraint(equalTo: self.bottomAnchor, constant: -20)
            ])
        } else {
            // 设置单个按钮布局
            NSLayoutConstraint.activate([
                titleLabel.topAnchor.constraint(equalTo: self.topAnchor, constant: 20),
                titleLabel.leadingAnchor.constraint(equalTo: self.leadingAnchor, constant: 20),
                titleLabel.trailingAnchor.constraint(equalTo: self.trailingAnchor, constant: -20),
                
                messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 10),
                messageLabel.leadingAnchor.constraint(equalTo: self.leadingAnchor, constant: 20),
                messageLabel.trailingAnchor.constraint(equalTo: self.trailingAnchor, constant: -20),
                
                confirmButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 20),
                confirmButton.leadingAnchor.constraint(equalTo: self.leadingAnchor, constant: 20),
                confirmButton.trailingAnchor.constraint(equalTo: self.trailingAnchor, constant: -20),
                confirmButton.bottomAnchor.constraint(equalTo: self.bottomAnchor, constant: -20)
            ])
        }
    }
    
    /// 确认按钮点击事件
    @objc private func confirmButtonTapped() {
        self.hide(animated: true) {
            self.confirmAction?()
        }
    }
    
    /// 取消按钮点击事件
    @objc private func cancelButtonTapped() {
        self.hide(animated: true) {
            self.cancelAction?()
        }
    }
    
    /// 设置确认回调
    /// - Parameter action: 确认回调
    func onConfirm(_ action: @escaping () -> Void) {
        self.confirmAction = action
    }
    
    /// 设置取消回调
    /// - Parameter action: 取消回调
    func onCancel(_ action: @escaping () -> Void) {
        self.cancelAction = action
    }
    
    /// 显示在视图控制器上
    /// - Parameters:
    ///   - viewController: 视图控制器
    ///   - animated: 是否使用动画
    func show(in viewController: UIViewController, animated: Bool = true) {
        // 创建背景视图
        let backgroundView = UIView(frame: viewController.view.bounds)
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        backgroundView.tag = 999
        backgroundView.alpha = 0
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        backgroundView.addGestureRecognizer(tapGesture)
        
        // 添加背景视图
        viewController.view.addSubview(backgroundView)
        
        // 添加警告视图
        self.translatesAutoresizingMaskIntoConstraints = false
        viewController.view.addSubview(self)
        
        // 设置约束
        NSLayoutConstraint.activate([
            self.centerXAnchor.constraint(equalTo: viewController.view.centerXAnchor),
            self.centerYAnchor.constraint(equalTo: viewController.view.centerYAnchor),
            self.widthAnchor.constraint(equalTo: viewController.view.widthAnchor, multiplier: 0.8)
        ])
        
        // 设置初始状态
        self.alpha = 0
        self.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        
        // 执行动画
        if animated {
            UIView.animate(withDuration: 0.3) {
                backgroundView.alpha = 1
                self.alpha = 1
                self.transform = CGAffineTransform.identity
            }
        } else {
            backgroundView.alpha = 1
            self.alpha = 1
            self.transform = CGAffineTransform.identity
        }
    }
    
    /// 从父视图中隐藏
    /// - Parameters:
    ///   - animated: 是否使用动画
    ///   - completion: 完成回调
    func hide(animated: Bool = true, completion: (() -> Void)? = nil) {
        // 获取背景视图
        if let backgroundView = self.superview?.viewWithTag(999) {
            if animated {
                UIView.animate(withDuration: 0.3, animations: {
                    backgroundView.alpha = 0
                    self.alpha = 0
                    self.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
                }) { _ in
                    backgroundView.removeFromSuperview()
                    self.removeFromSuperview()
                    completion?()
                }
            } else {
                backgroundView.removeFromSuperview()
                self.removeFromSuperview()
                completion?()
            }
        }
    }
    
    /// 背景点击事件
    @objc private func backgroundTapped() {
        self.hide(animated: true) {
            self.cancelAction?()
        }
    }
}
