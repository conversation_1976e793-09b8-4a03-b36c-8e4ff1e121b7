//
//  MHTheme.swift
//  maohuoban
//
//  Created by 🐯 on 2025/5/13.
//  文件作用：定义应用的主题系统，包括颜色、字体、间距等设计规范
//  重构说明：从HomeView.swift中分离出来，实现主题系统的模块化管理
//

import SwiftUI
import UIKit

// MARK: - 主题系统
/// 毛伙伴应用主题系统
/// 提供统一的颜色、字体、间距等设计规范
/// 支持深色模式自适应
struct MHTheme {
    
    // MARK: - 主要颜色
    /// 主题色 - 疗愈蓝 (在深色模式下稍微调亮)
    /// 用于主要按钮、强调元素等
    static let primaryColor = Color(red: 0.0, green: 0.48, blue: 0.8)
    
    /// 次要颜色 - 红色
    /// 用于警告、错误提示等
    static let secondaryColor = Color.red
    
    // MARK: - 背景颜色 (自适应深色模式)
    /// 主背景色 - 自适应系统背景色
    static let backgroundColor = Color(UIColor.systemBackground)
    
    /// 次要背景色 - 用于卡片、面板等
    static let secondaryBackgroundColor = Color(UIColor.secondarySystemBackground)
    
    /// 三级背景色 - 用于分组、区域划分等
    static let tertiaryBackgroundColor = Color(UIColor.tertiarySystemBackground)
    
    // MARK: - 文本颜色 (自适应深色模式)
    /// 主要文本颜色 - 用于标题、重要文本
    static let primaryTextColor = Color(UIColor.label)
    
    /// 次要文本颜色 - 用于副标题、说明文本
    static let secondaryTextColor = Color(UIColor.secondaryLabel)
    
    /// 三级文本颜色 - 用于占位符、辅助信息
    static let tertiaryTextColor = Color(UIColor.tertiaryLabel)
    
    // MARK: - 分割线和边框颜色
    /// 分割线颜色 - 用于列表分割线
    static let separatorColor = Color(UIColor.separator)
    
    /// 边框颜色 - 用于输入框、按钮边框等
    static let borderColor = Color(UIColor.separator).opacity(0.5)
    
    // MARK: - 灰色系列
    /// 浅灰色背景 - 用于占位图片、禁用状态等
    static let lightGrayColor = Color(UIColor.systemGray6)
    
    /// 中等灰色背景 - 用于次要元素背景
    static let mediumGrayColor = Color(UIColor.systemGray5)
    
    /// 深灰色背景 - 用于强调的次要元素
    static let darkGrayColor = Color(UIColor.systemGray4)
    
    // MARK: - 字体系统
    /// 大标题字体
    static let largeTitleFont = Font.system(size: 28, weight: .bold)
    
    /// 标题字体
    static let titleFont = Font.system(size: 22, weight: .semibold)
    
    /// 副标题字体
    static let subtitleFont = Font.system(size: 18, weight: .medium)
    
    /// 正文字体
    static let bodyFont = Font.system(size: 16, weight: .regular)
    
    /// 说明文字字体
    static let captionFont = Font.system(size: 14, weight: .regular)
    
    /// 小号文字字体
    static let footnoteFont = Font.system(size: 12, weight: .regular)
    
    // MARK: - 间距系统
    /// 极小间距 - 4pt
    static let spacingXS: CGFloat = 4
    
    /// 小间距 - 8pt
    static let spacingS: CGFloat = 8
    
    /// 中等间距 - 16pt
    static let spacingM: CGFloat = 16
    
    /// 大间距 - 24pt
    static let spacingL: CGFloat = 24
    
    /// 极大间距 - 32pt
    static let spacingXL: CGFloat = 32
    
    // MARK: - 圆角系统
    /// 小圆角 - 4pt
    static let cornerRadiusS: CGFloat = 4
    
    /// 中等圆角 - 8pt
    static let cornerRadiusM: CGFloat = 8
    
    /// 大圆角 - 12pt
    static let cornerRadiusL: CGFloat = 12
    
    /// 极大圆角 - 16pt
    static let cornerRadiusXL: CGFloat = 16
    
    // MARK: - 阴影系统
    /// 轻微阴影 - 用于卡片等
    static let shadowLight = (color: Color.black.opacity(0.1), radius: CGFloat(5), x: CGFloat(0), y: CGFloat(-2))
    
    /// 中等阴影 - 用于浮动元素
    static let shadowMedium = (color: Color.black.opacity(0.15), radius: CGFloat(10), x: CGFloat(0), y: CGFloat(4))
    
    /// 重阴影 - 用于模态框等
    static let shadowHeavy = (color: Color.black.opacity(0.25), radius: CGFloat(20), x: CGFloat(0), y: CGFloat(8))
}

// MARK: - 主题管理器
/// 主题管理器 - 负责主题的切换和状态管理
class MHThemeManager: ObservableObject {
    /// 单例实例
    static let shared = MHThemeManager()
    
    /// 当前主题模式
    @Published var isDarkMode: Bool = false
    
    /// 私有初始化方法
    private init() {
        // 从系统设置中获取当前主题模式
        isDarkMode = UITraitCollection.current.userInterfaceStyle == .dark
    }
    
    /// 切换主题模式
    /// - Parameter isDark: 是否为深色模式
    func setTheme(isDark: Bool) {
        isDarkMode = isDark
        // 这里可以添加主题切换的动画效果
        withAnimation(.easeInOut(duration: 0.3)) {
            // 主题切换动画
        }
    }
    
    /// 跟随系统主题
    func followSystemTheme() {
        isDarkMode = UITraitCollection.current.userInterfaceStyle == .dark
    }
}

// MARK: - 主题扩展
extension View {
    /// 应用主题阴影
    /// - Parameter style: 阴影样式 (light, medium, heavy)
    func themeShadow(_ style: String = "light") -> some View {
        let shadow: (color: Color, radius: CGFloat, x: CGFloat, y: CGFloat)
        
        switch style {
        case "medium":
            shadow = MHTheme.shadowMedium
        case "heavy":
            shadow = MHTheme.shadowHeavy
        default:
            shadow = MHTheme.shadowLight
        }
        
        return self.shadow(
            color: shadow.color,
            radius: shadow.radius,
            x: shadow.x,
            y: shadow.y
        )
    }
}
