// swift-tools-version:5.5
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "maohuoban",
    platforms: [
        .iOS(.v14)
    ],
    products: [
        .library(
            name: "maohuoban",
            targets: ["maohuoban"]),
    ],
    dependencies: [
        // 依赖项
    ],
    targets: [
        .target(
            name: "maohuoban",
            dependencies: [],
            path: "maohuoban",
            exclude: ["Legacy"],
            resources: [
                .process("SupportingFiles")
            ]
        ),
        .testTarget(
            name: "maohuobanTests",
            dependencies: ["maohuoban"],
            path: "maohuobanTests"
        ),
    ]
)
